/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ProjectExtendedResponseModelTargetAudience } from "./ProjectExtendedResponseModelTargetAudience";
import { ProjectState } from "./ProjectState";
import { ProjectExtendedResponseModelAccessLevel } from "./ProjectExtendedResponseModelAccessLevel";
import { ProjectExtendedResponseModelFiction } from "./ProjectExtendedResponseModelFiction";
import { ProjectCreationMetaResponseModel } from "./ProjectCreationMetaResponseModel";
import { ProjectExtendedResponseModelSourceType } from "./ProjectExtendedResponseModelSourceType";
import { CaptionStyleModel } from "./CaptionStyleModel";
import { ProjectExtendedResponseModelQualityPreset } from "./ProjectExtendedResponseModelQualityPreset";
import { ChapterResponse } from "./ChapterResponse";
import { PronunciationDictionaryVersionResponseModel } from "./PronunciationDictionaryVersionResponseModel";
import { PronunciationDictionaryLocatorResponseModel } from "./PronunciationDictionaryLocatorResponseModel";
import { ProjectExtendedResponseModelApplyTextNormalization } from "./ProjectExtendedResponseModelApplyTextNormalization";
export declare const ProjectExtendedResponse: core.serialization.ObjectSchema<serializers.ProjectExtendedResponse.Raw, ElevenLabs.ProjectExtendedResponse>;
export declare namespace ProjectExtendedResponse {
    interface Raw {
        project_id: string;
        name: string;
        create_date_unix: number;
        default_title_voice_id: string;
        default_paragraph_voice_id: string;
        default_model_id: string;
        last_conversion_date_unix?: number | null;
        can_be_downloaded: boolean;
        title?: string | null;
        author?: string | null;
        description?: string | null;
        genres?: string[] | null;
        cover_image_url?: string | null;
        target_audience?: ProjectExtendedResponseModelTargetAudience.Raw | null;
        language?: string | null;
        content_type?: string | null;
        original_publication_date?: string | null;
        mature_content?: boolean | null;
        isbn_number?: string | null;
        volume_normalization: boolean;
        state: ProjectState.Raw;
        access_level: ProjectExtendedResponseModelAccessLevel.Raw;
        fiction?: ProjectExtendedResponseModelFiction.Raw | null;
        quality_check_on: boolean;
        quality_check_on_when_bulk_convert: boolean;
        creation_meta?: ProjectCreationMetaResponseModel.Raw | null;
        source_type?: ProjectExtendedResponseModelSourceType.Raw | null;
        chapters_enabled?: boolean | null;
        captions_enabled?: boolean | null;
        caption_style?: CaptionStyleModel.Raw | null;
        public_share_id?: string | null;
        quality_preset: ProjectExtendedResponseModelQualityPreset.Raw;
        chapters: ChapterResponse.Raw[];
        pronunciation_dictionary_versions: PronunciationDictionaryVersionResponseModel.Raw[];
        pronunciation_dictionary_locators: PronunciationDictionaryLocatorResponseModel.Raw[];
        apply_text_normalization: ProjectExtendedResponseModelApplyTextNormalization.Raw;
        experimental?: Record<string, unknown> | null;
    }
}
