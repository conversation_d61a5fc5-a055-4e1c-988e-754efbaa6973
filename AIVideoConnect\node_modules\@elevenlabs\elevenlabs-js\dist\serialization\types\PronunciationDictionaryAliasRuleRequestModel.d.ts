/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PronunciationDictionaryAliasRuleRequestModel: core.serialization.ObjectSchema<serializers.PronunciationDictionaryAliasRuleRequestModel.Raw, ElevenLabs.PronunciationDictionaryAliasRuleRequestModel>;
export declare namespace PronunciationDictionaryAliasRuleRequestModel {
    interface Raw {
        string_to_replace: string;
        alias: string;
    }
}
