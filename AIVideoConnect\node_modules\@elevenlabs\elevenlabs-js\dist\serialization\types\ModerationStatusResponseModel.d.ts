/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ModerationStatusResponseModelSafetyStatus } from "./ModerationStatusResponseModelSafetyStatus";
import { ModerationStatusResponseModelWarningStatus } from "./ModerationStatusResponseModelWarningStatus";
export declare const ModerationStatusResponseModel: core.serialization.ObjectSchema<serializers.ModerationStatusResponseModel.Raw, ElevenLabs.ModerationStatusResponseModel>;
export declare namespace ModerationStatusResponseModel {
    interface Raw {
        is_in_probation: boolean;
        enterprise_check_nogo_voice: boolean;
        enterprise_check_block_nogo_voice: boolean;
        never_live_moderate: boolean;
        nogo_voice_similar_voice_upload_count: number;
        enterprise_background_moderation_enabled: boolean;
        safety_status?: ModerationStatusResponseModelSafetyStatus.Raw | null;
        warning_status?: ModerationStatusResponseModelWarningStatus.Raw | null;
        on_watchlist: boolean;
    }
}
