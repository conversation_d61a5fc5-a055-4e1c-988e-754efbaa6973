import * as THREE from 'three';

interface OrbControl {
  setState(state: "welcome" | "discovery" | "coaching" | "reveal" | "upsell" | "idle"): void;
  onTTSFrame(data: { time: number; rms: number; pitch: number; viseme?: string }): void;
  setEngagement(x: number): void;
  setIntensity(x: number): void;
  dispose(): void;
}

export function createOrbRenderer(container: HTMLElement): OrbControl {
  // Scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
  
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setClearColor(0x000000, 0);
  container.appendChild(renderer.domElement);

  // Orb geometry and material
  const orbGeometry = new THREE.SphereGeometry(1, 64, 64);
  
  // Custom shader material for the orb
  const orbMaterial = new THREE.ShaderMaterial({
    transparent: true,
    uniforms: {
      uTime: { value: 0 },
      uRMS: { value: 0 },
      uPitch: { value: 0 },
      uHueBase: { value: 0.55 }, // cyan/blue
      uHueShift: { value: 0.0 },
      uEngagement: { value: 0.8 },
      uIntensity: { value: 0.5 },
    },
    vertexShader: `
      varying vec3 vPos;
      varying vec3 vNormal;
      
      void main() {
        vPos = position;
        vNormal = normal;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float uTime;
      uniform float uRMS;
      uniform float uPitch;
      uniform float uHueBase;
      uniform float uHueShift;
      uniform float uEngagement;
      uniform float uIntensity;
      
      varying vec3 vPos;
      varying vec3 vNormal;
      
      // Simple noise function
      float hash(vec3 p) {
        return fract(sin(dot(p, vec3(12.9898, 78.233, 37.719))) * 43758.5453);
      }
      
      float noise(vec3 p) {
        vec3 i = floor(p);
        vec3 f = fract(p);
        
        float n = mix(
          mix(mix(hash(i + vec3(0,0,0)), hash(i + vec3(1,0,0)), f.x),
              mix(hash(i + vec3(0,1,0)), hash(i + vec3(1,1,0)), f.x), f.y),
          mix(mix(hash(i + vec3(0,0,1)), hash(i + vec3(1,0,1)), f.x),
              mix(hash(i + vec3(0,1,1)), hash(i + vec3(1,1,1)), f.x), f.y), f.z);
        return n;
      }
      
      vec3 hsv2rgb(vec3 c) {
        vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
        vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
        return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
      }
      
      void main() {
        vec3 p = normalize(vPos);
        
        // Fresnel effect
        float fresnel = pow(1.0 - abs(dot(vNormal, vec3(0, 0, 1))), 2.0);
        
        // Noise for surface distortion
        float n = noise(p * 3.0 + vec3(uTime * 0.5)) * 0.15;
        
        // Base color with hue shifting
        float hue = uHueBase + uHueShift + n * 0.1;
        vec3 baseColor = hsv2rgb(vec3(hue, 0.8, 1.0));
        
        // Combine effects
        float alpha = clamp(0.3 + fresnel * 0.7 + uRMS * 0.6 + n, 0.0, 1.0);
        vec3 finalColor = baseColor * (0.6 + uRMS * 0.8) * uEngagement;
        
        gl_FragColor = vec4(finalColor, alpha * 0.8);
      }
    `
  });

  const orb = new THREE.Mesh(orbGeometry, orbMaterial);
  scene.add(orb);

  // Particle system for aura
  const particleCount = 100;
  const particles = new THREE.BufferGeometry();
  const positions = new Float32Array(particleCount * 3);
  const velocities = new Float32Array(particleCount * 3);

  for (let i = 0; i < particleCount; i++) {
    const i3 = i * 3;
    // Random positions around sphere
    const radius = 1.5 + Math.random() * 0.5;
    const theta = Math.random() * Math.PI * 2;
    const phi = Math.random() * Math.PI;
    
    positions[i3] = radius * Math.sin(phi) * Math.cos(theta);
    positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
    positions[i3 + 2] = radius * Math.cos(phi);
    
    velocities[i3] = (Math.random() - 0.5) * 0.02;
    velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;
    velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
  }

  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  particles.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

  const particleMaterial = new THREE.PointsMaterial({
    color: 0x00E6B8,
    size: 0.02,
    transparent: true,
    opacity: 0.6,
    blending: THREE.AdditiveBlending,
  });

  const particleSystem = new THREE.Points(particles, particleMaterial);
  scene.add(particleSystem);

  camera.position.z = 3;

  // Animation state
  let currentState = "idle";
  let animationId: number;

  const animate = () => {
    animationId = requestAnimationFrame(animate);
    
    const time = Date.now() * 0.001;
    orbMaterial.uniforms.uTime.value = time;
    
    // Rotate orb slowly
    orb.rotation.y += 0.005;
    
    // Animate particles
    const positions = particleSystem.geometry.attributes.position.array as Float32Array;
    const velocities = particleSystem.geometry.attributes.velocity.array as Float32Array;
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      positions[i3] += velocities[i3];
      positions[i3 + 1] += velocities[i3 + 1];
      positions[i3 + 2] += velocities[i3 + 2];
      
      // Keep particles in bounds
      const radius = Math.sqrt(positions[i3]**2 + positions[i3 + 1]**2 + positions[i3 + 2]**2);
      if (radius > 2.5 || radius < 1.2) {
        velocities[i3] *= -1;
        velocities[i3 + 1] *= -1;
        velocities[i3 + 2] *= -1;
      }
    }
    
    particleSystem.geometry.attributes.position.needsUpdate = true;
    
    renderer.render(scene, camera);
  };

  animate();

  // Resize handler
  const handleResize = () => {
    const width = container.clientWidth;
    const height = container.clientHeight;
    camera.aspect = width / height;
    camera.updateProjectionMatrix();
    renderer.setSize(width, height);
  };

  window.addEventListener('resize', handleResize);

  // Control interface
  const control: OrbControl = {
    setState(state) {
      currentState = state;
      
      // Update colors based on state
      const stateColors = {
        welcome: { hue: 0.55, intensity: 0.6 }, // Teal
        discovery: { hue: 0.5, intensity: 0.7 }, // Cyan
        coaching: { hue: 0.6, intensity: 0.9 }, // Blue
        reveal: { hue: 0.45, intensity: 0.8 }, // Aqua
        upsell: { hue: 0.65, intensity: 0.7 }, // Royal blue
        idle: { hue: 0.55, intensity: 0.5 }
      };
      
      const config = stateColors[state] || stateColors.idle;
      orbMaterial.uniforms.uHueBase.value = config.hue;
      orbMaterial.uniforms.uIntensity.value = config.intensity;
    },

    onTTSFrame(data) {
      orbMaterial.uniforms.uRMS.value = data.rms;
      orbMaterial.uniforms.uHueShift.value = (data.pitch - 180) / 600.0;
      
      // Scale orb based on audio
      const scale = 1.0 + data.rms * 0.3;
      orb.scale.setScalar(scale);
      
      // Update particle opacity
      particleMaterial.opacity = 0.6 + data.rms * 0.4;
    },

    setEngagement(x) {
      orbMaterial.uniforms.uEngagement.value = x;
    },

    setIntensity(x) {
      orbMaterial.uniforms.uIntensity.value = x;
    },

    dispose() {
      cancelAnimationFrame(animationId);
      window.removeEventListener('resize', handleResize);
      container.removeChild(renderer.domElement);
      renderer.dispose();
      orbGeometry.dispose();
      orbMaterial.dispose();
      particles.dispose();
      particleMaterial.dispose();
    }
  };

  return control;
}
