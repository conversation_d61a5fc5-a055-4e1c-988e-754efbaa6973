/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PostAgentAvatarResponseModel: core.serialization.ObjectSchema<serializers.PostAgentAvatarResponseModel.Raw, ElevenLabs.PostAgentAvatarResponseModel>;
export declare namespace PostAgentAvatarResponseModel {
    interface Raw {
        agent_id: string;
        avatar_url?: string | null;
    }
}
