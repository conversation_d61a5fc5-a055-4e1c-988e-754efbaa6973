/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../../../index";
import * as ElevenLabs from "../../../../../../../../../api/index";
import * as core from "../../../../../../../../../core";
export declare const LlmUsageCalculatorRequestModel: core.serialization.Schema<serializers.conversationalAi.agents.LlmUsageCalculatorRequestModel.Raw, ElevenLabs.conversationalAi.agents.LlmUsageCalculatorRequestModel>;
export declare namespace LlmUsageCalculatorRequestModel {
    interface Raw {
        prompt_length?: number | null;
        number_of_pages?: number | null;
        rag_enabled?: boolean | null;
    }
}
