/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { GetPhoneNumberTwilioResponseModel } from "../../../../../types/GetPhoneNumberTwilioResponseModel";
import { GetPhoneNumberSipTrunkResponseModel } from "../../../../../types/GetPhoneNumberSipTrunkResponseModel";
export declare const PhoneNumbersGetResponse: core.serialization.Schema<serializers.conversationalAi.PhoneNumbersGetResponse.Raw, ElevenLabs.conversationalAi.PhoneNumbersGetResponse>;
export declare namespace PhoneNumbersGetResponse {
    type Raw = PhoneNumbersGetResponse.Twilio | PhoneNumbersGetResponse.SipTrunk;
    interface Twilio extends GetPhoneNumberTwilioResponseModel.Raw {
        provider: "twilio";
    }
    interface SipTrunk extends GetPhoneNumberSipTrunkResponseModel.Raw {
        provider: "sip_trunk";
    }
}
