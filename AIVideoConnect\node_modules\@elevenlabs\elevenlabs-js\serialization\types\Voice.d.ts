/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { VoiceSample } from "./VoiceSample";
import { VoiceResponseModelCategory } from "./VoiceResponseModelCategory";
import { FineTuningResponse } from "./FineTuningResponse";
import { VoiceSettings } from "./VoiceSettings";
import { VoiceSharingResponse } from "./VoiceSharingResponse";
import { VerifiedVoiceLanguageResponseModel } from "./VerifiedVoiceLanguageResponseModel";
import { VoiceResponseModelSafetyControl } from "./VoiceResponseModelSafetyControl";
import { VoiceVerificationResponse } from "./VoiceVerificationResponse";
export declare const Voice: core.serialization.ObjectSchema<serializers.Voice.Raw, ElevenLabs.Voice>;
export declare namespace Voice {
    interface Raw {
        voice_id: string;
        name?: string | null;
        samples?: VoiceSample.Raw[] | null;
        category?: VoiceResponseModelCategory.Raw | null;
        fine_tuning?: FineTuningResponse.Raw | null;
        labels?: Record<string, string> | null;
        description?: string | null;
        preview_url?: string | null;
        available_for_tiers?: string[] | null;
        settings?: VoiceSettings.Raw | null;
        sharing?: VoiceSharingResponse.Raw | null;
        high_quality_base_model_ids?: string[] | null;
        verified_languages?: VerifiedVoiceLanguageResponseModel.Raw[] | null;
        safety_control?: VoiceResponseModelSafetyControl.Raw | null;
        voice_verification?: VoiceVerificationResponse.Raw | null;
        permission_on_resource?: string | null;
        is_owner?: boolean | null;
        is_legacy?: boolean | null;
        is_mixed?: boolean | null;
        favorited_at_unix?: number | null;
        created_at_unix?: number | null;
    }
}
