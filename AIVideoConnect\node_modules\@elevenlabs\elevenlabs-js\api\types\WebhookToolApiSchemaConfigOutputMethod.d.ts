/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The HTTP method to use for the webhook
 */
export type WebhookToolApiSchemaConfigOutputMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
export declare const WebhookToolApiSchemaConfigOutputMethod: {
    readonly Get: "GET";
    readonly Post: "POST";
    readonly Put: "PUT";
    readonly Patch: "PATCH";
    readonly Delete: "DELETE";
};
