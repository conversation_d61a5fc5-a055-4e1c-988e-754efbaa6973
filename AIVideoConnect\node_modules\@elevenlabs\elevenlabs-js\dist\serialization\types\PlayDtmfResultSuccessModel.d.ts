/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PlayDtmfResultSuccessModel: core.serialization.ObjectSchema<serializers.PlayDtmfResultSuccessModel.Raw, ElevenLabs.PlayDtmfResultSuccessModel>;
export declare namespace PlayDtmfResultSuccessModel {
    interface Raw {
        status?: "success" | null;
        dtmf_tones: string;
        reason?: string | null;
    }
}
