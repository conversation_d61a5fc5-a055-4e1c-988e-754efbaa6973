/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PlayDtmfToolConfig: core.serialization.ObjectSchema<serializers.PlayDtmfToolConfig.Raw, ElevenLabs.PlayDtmfToolConfig>;
export declare namespace PlayDtmfToolConfig {
    interface Raw {
    }
}
