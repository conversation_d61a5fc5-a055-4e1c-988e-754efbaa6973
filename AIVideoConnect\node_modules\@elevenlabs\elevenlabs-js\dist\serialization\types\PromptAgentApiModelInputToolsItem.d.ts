/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ClientToolConfigInput } from "./ClientToolConfigInput";
import { SystemToolConfigInput } from "./SystemToolConfigInput";
import { WebhookToolConfigInput } from "./WebhookToolConfigInput";
export declare const PromptAgentApiModelInputToolsItem: core.serialization.Schema<serializers.PromptAgentApiModelInputToolsItem.Raw, ElevenLabs.PromptAgentApiModelInputToolsItem>;
export declare namespace PromptAgentApiModelInputToolsItem {
    type Raw = PromptAgentApiModelInputToolsItem.Client | PromptAgentApiModelInputToolsItem.Mcp | PromptAgentApiModelInputToolsItem.System | PromptAgentApiModelInputToolsItem.Webhook;
    interface Client extends ClientToolConfigInput.Raw {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends SystemToolConfigInput.Raw {
        type: "system";
    }
    interface Webhook extends WebhookToolConfigInput.Raw {
        type: "webhook";
    }
}
