/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoicePreviewResponseModel: core.serialization.ObjectSchema<serializers.VoicePreviewResponseModel.Raw, ElevenLabs.VoicePreviewResponseModel>;
export declare namespace VoicePreviewResponseModel {
    interface Raw {
        audio_base_64: string;
        generated_voice_id: string;
        media_type: string;
        duration_secs: number;
        language?: string | null;
    }
}
