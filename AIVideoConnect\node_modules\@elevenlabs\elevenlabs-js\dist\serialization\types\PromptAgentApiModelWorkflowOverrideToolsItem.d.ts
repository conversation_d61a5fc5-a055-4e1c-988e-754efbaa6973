/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ClientToolConfigInput } from "./ClientToolConfigInput";
import { SystemToolConfigInput } from "./SystemToolConfigInput";
import { WebhookToolConfigInput } from "./WebhookToolConfigInput";
export declare const PromptAgentApiModelWorkflowOverrideToolsItem: core.serialization.Schema<serializers.PromptAgentApiModelWorkflowOverrideToolsItem.Raw, ElevenLabs.PromptAgentApiModelWorkflowOverrideToolsItem>;
export declare namespace PromptAgentApiModelWorkflowOverrideToolsItem {
    type Raw = PromptAgentApiModelWorkflowOverrideToolsItem.Client | PromptAgentApiModelWorkflowOverrideToolsItem.Mcp | PromptAgentApiModelWorkflowOverrideToolsItem.System | PromptAgentApiModelWorkflowOverrideToolsItem.Webhook;
    interface Client extends ClientToolConfigInput.Raw {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends SystemToolConfigInput.Raw {
        type: "system";
    }
    interface Webhook extends WebhookToolConfigInput.Raw {
        type: "webhook";
    }
}
