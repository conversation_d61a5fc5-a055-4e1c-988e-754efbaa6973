/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
import { McpServerConfigInput } from "../../../../../../types/McpServerConfigInput";
export declare const McpServerRequestModel: core.serialization.Schema<serializers.conversationalAi.McpServerRequestModel.Raw, ElevenLabs.conversationalAi.McpServerRequestModel>;
export declare namespace McpServerRequestModel {
    interface Raw {
        config: McpServerConfigInput.Raw;
    }
}
