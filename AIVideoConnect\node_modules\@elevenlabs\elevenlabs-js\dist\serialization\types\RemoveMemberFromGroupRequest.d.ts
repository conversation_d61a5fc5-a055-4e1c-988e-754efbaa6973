/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RemoveMemberFromGroupRequest: core.serialization.Schema<serializers.RemoveMemberFromGroupRequest.Raw, ElevenLabs.RemoveMemberFromGroupRequest>;
export declare namespace RemoveMemberFromGroupRequest {
    type Raw = unknown;
}
