/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ToolType: core.serialization.Schema<serializers.ToolType.Raw, ElevenLabs.ToolType>;
export declare namespace ToolType {
    type Raw = "system" | "webhook" | "client" | "mcp" | "workflow";
}
