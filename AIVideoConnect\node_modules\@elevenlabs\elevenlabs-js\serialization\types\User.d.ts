/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SubscriptionResponse } from "./SubscriptionResponse";
import { SubscriptionExtrasResponseModel } from "./SubscriptionExtrasResponseModel";
export declare const User: core.serialization.ObjectSchema<serializers.User.Raw, ElevenLabs.User>;
export declare namespace User {
    interface Raw {
        user_id: string;
        subscription: SubscriptionResponse.Raw;
        subscription_extras?: SubscriptionExtrasResponseModel.Raw | null;
        is_new_user: boolean;
        xi_api_key?: string | null;
        can_use_delayed_payment_methods: boolean;
        is_onboarding_completed: boolean;
        is_onboarding_checklist_completed: boolean;
        first_name?: string | null;
        is_api_key_hashed?: boolean | null;
        xi_api_key_preview?: string | null;
        referral_link_code?: string | null;
        partnerstack_partner_default_link?: string | null;
        created_at: number;
    }
}
