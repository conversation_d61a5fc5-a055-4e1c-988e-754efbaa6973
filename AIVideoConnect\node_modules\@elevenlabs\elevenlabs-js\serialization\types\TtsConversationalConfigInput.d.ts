/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { TtsConversationalModel } from "./TtsConversationalModel";
import { SupportedVoice } from "./SupportedVoice";
import { TtsOutputFormat } from "./TtsOutputFormat";
import { TtsOptimizeStreamingLatency } from "./TtsOptimizeStreamingLatency";
import { PydanticPronunciationDictionaryVersionLocator } from "./PydanticPronunciationDictionaryVersionLocator";
export declare const TtsConversationalConfigInput: core.serialization.ObjectSchema<serializers.TtsConversationalConfigInput.Raw, ElevenLabs.TtsConversationalConfigInput>;
export declare namespace TtsConversationalConfigInput {
    interface Raw {
        model_id?: TtsConversationalModel.Raw | null;
        voice_id?: string | null;
        supported_voices?: SupportedVoice.Raw[] | null;
        agent_output_audio_format?: TtsOutputFormat.Raw | null;
        optimize_streaming_latency?: TtsOptimizeStreamingLatency.Raw | null;
        stability?: number | null;
        speed?: number | null;
        similarity_boost?: number | null;
        pronunciation_dictionary_locators?: PydanticPronunciationDictionaryVersionLocator.Raw[] | null;
    }
}
