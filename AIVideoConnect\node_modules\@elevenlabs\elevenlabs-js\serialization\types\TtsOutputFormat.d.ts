/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TtsOutputFormat: core.serialization.Schema<serializers.TtsOutputFormat.Raw, ElevenLabs.TtsOutputFormat>;
export declare namespace TtsOutputFormat {
    type Raw = "pcm_8000" | "pcm_16000" | "pcm_22050" | "pcm_24000" | "pcm_44100" | "pcm_48000" | "ulaw_8000";
}
