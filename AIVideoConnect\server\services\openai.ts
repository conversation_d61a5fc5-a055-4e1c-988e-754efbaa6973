import OpenAI from "openai";

// the newest OpenAI model is "gpt-5" which was released August 7, 2025. do not change this unless explicitly requested by the user
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_ENV_VAR || "default_key"
});

export async function analyzeIntakeData(intake: any): Promise<{
  constraints: any;
  levers: any;
  emotionalSignals: any;
}> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are a fitness assessment expert. Analyze the user's intake data and extract constraints, levers, and emotional signals. Respond with JSON in this format: { 'constraints': {}, 'levers': {}, 'emotionalSignals': {} }"
        },
        {
          role: "user",
          content: `Analyze this fitness assessment intake: ${JSON.stringify(intake)}`
        }
      ],
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || "{}");
  } catch (error: any) {
    throw new Error("Failed to analyze intake data: " + error.message);
  }
}

export async function generateWorkoutPlan(sessionData: any, scores: any): Promise<any> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are an expert fitness coach. Generate a detailed, personalized workout plan based on the assessment data and scores. Include weekly schedule, exercise selections, progressions, and nutrition guidelines. Respond with structured JSON."
        },
        {
          role: "user",
          content: `Generate a workout plan for this assessment data: ${JSON.stringify({ sessionData, scores })}`
        }
      ],
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || "{}");
  } catch (error: any) {
    throw new Error("Failed to generate workout plan: " + error.message);
  }
}

export async function analyzeMovementQuality(movementData: any): Promise<{
  scores: any;
  recommendations: string[];
}> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are a movement analysis expert. Analyze pose detection data and provide movement quality scores and recommendations. Respond with JSON: { 'scores': {}, 'recommendations': [] }"
        },
        {
          role: "user",
          content: `Analyze this movement data: ${JSON.stringify(movementData)}`
        }
      ],
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || "{}");
  } catch (error: any) {
    throw new Error("Failed to analyze movement quality: " + error.message);
  }
}

export async function generateTTS(text: string, voice: string = "alloy"): Promise<Buffer> {
  try {
    const response = await openai.audio.speech.create({
      model: "tts-1",
      voice: voice as any, // OpenAI supports: alloy, echo, fable, onyx, nova, shimmer
      input: text,
    });

    const buffer = Buffer.from(await response.arrayBuffer());
    return buffer;
  } catch (error: any) {
    throw new Error("Failed to generate TTS: " + error.message);
  }
}

export async function generateAIResponse(userMessage: string, phase: string = 'welcome'): Promise<string> {
  try {
    const systemPrompts = {
      welcome: "You are an energetic AI fitness trainer starting a personalized assessment. Be welcoming and motivating. Keep responses under 2 sentences.",
      discovery: "You are asking discovery questions about fitness goals and schedule. Be encouraging and ask follow-up questions. Keep responses under 2 sentences.", 
      movement: "You are coaching someone through exercises, providing real-time form feedback. Be supportive and give specific movement cues. Keep responses under 2 sentences.",
      photo: "You are guiding someone through posture photos for analysis. Be clear about positioning. Keep responses under 2 sentences.",
      reveal: "You are revealing fitness assessment results and workout recommendations. Be encouraging and specific. Keep responses under 2 sentences."
    };

    const completion = await openai.chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: systemPrompts[phase as keyof typeof systemPrompts] || systemPrompts.welcome
        },
        {
          role: "user", 
          content: userMessage
        }
      ],
      max_tokens: 100,
      temperature: 0.7,
    });

    return completion.choices[0]?.message?.content || "Great! Let's continue with your assessment.";
  } catch (error: any) {
    console.error('OpenAI conversation error:', error);
    return "Perfect! Let's keep moving forward with your fitness journey.";
  }
}
