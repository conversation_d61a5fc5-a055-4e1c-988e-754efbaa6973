/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ConversationInitiationClientDataRequestInput } from "./ConversationInitiationClientDataRequestInput";
export declare const OutboundCallRecipient: core.serialization.ObjectSchema<serializers.OutboundCallRecipient.Raw, ElevenLabs.OutboundCallRecipient>;
export declare namespace OutboundCallRecipient {
    interface Raw {
        id?: string | null;
        phone_number: string;
        conversation_initiation_client_data?: ConversationInitiationClientDataRequestInput.Raw | null;
    }
}
