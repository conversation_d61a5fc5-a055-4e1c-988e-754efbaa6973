/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ResourceAccessInfoRole: core.serialization.Schema<serializers.ResourceAccessInfoRole.Raw, ElevenLabs.ResourceAccessInfoRole>;
export declare namespace ResourceAccessInfoRole {
    type Raw = "admin" | "editor" | "viewer";
}
