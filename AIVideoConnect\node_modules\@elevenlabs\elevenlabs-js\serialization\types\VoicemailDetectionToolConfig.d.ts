/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoicemailDetectionToolConfig: core.serialization.ObjectSchema<serializers.VoicemailDetectionToolConfig.Raw, ElevenLabs.VoicemailDetectionToolConfig>;
export declare namespace VoicemailDetectionToolConfig {
    interface Raw {
        voicemail_message?: string | null;
    }
}
