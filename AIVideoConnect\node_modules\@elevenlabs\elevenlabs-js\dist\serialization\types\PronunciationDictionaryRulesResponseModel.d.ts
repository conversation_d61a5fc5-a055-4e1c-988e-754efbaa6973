/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PronunciationDictionaryRulesResponseModel: core.serialization.ObjectSchema<serializers.PronunciationDictionaryRulesResponseModel.Raw, ElevenLabs.PronunciationDictionaryRulesResponseModel>;
export declare namespace PronunciationDictionaryRulesResponseModel {
    interface Raw {
        id: string;
        version_id: string;
        version_rules_num: number;
    }
}
