/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
import { Model } from "../../../types/Model";
export declare const Response: core.serialization.Schema<serializers.models.list.Response.Raw, ElevenLabs.Model[]>;
export declare namespace Response {
    type Raw = Model.Raw[];
}
