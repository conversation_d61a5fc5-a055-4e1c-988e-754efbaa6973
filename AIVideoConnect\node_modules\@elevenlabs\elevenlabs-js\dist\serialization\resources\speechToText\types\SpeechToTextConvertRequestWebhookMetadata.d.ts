/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const SpeechToTextConvertRequestWebhookMetadata: core.serialization.Schema<serializers.SpeechToTextConvertRequestWebhookMetadata.Raw, ElevenLabs.SpeechToTextConvertRequestWebhookMetadata>;
export declare namespace SpeechToTextConvertRequestWebhookMetadata {
    type Raw = string | Record<string, unknown>;
}
