/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const MetricType: core.serialization.Schema<serializers.MetricType.Raw, ElevenLabs.MetricType>;
export declare namespace MetricType {
    type Raw = "credits" | "minutes_used" | "request_count" | "ttfb_avg" | "ttfb_p95" | "fiat_units_spent" | "concurrency" | "concurrency_average";
}
