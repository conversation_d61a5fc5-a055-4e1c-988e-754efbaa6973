/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TtsOptimizeStreamingLatency: core.serialization.Schema<serializers.TtsOptimizeStreamingLatency.Raw, ElevenLabs.TtsOptimizeStreamingLatency>;
export declare namespace TtsOptimizeStreamingLatency {
    type Raw = number;
}
