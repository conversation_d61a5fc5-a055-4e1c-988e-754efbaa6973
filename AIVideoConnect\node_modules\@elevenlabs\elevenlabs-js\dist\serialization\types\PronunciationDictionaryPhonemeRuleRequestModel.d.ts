/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PronunciationDictionaryPhonemeRuleRequestModel: core.serialization.ObjectSchema<serializers.PronunciationDictionaryPhonemeRuleRequestModel.Raw, ElevenLabs.PronunciationDictionaryPhonemeRuleRequestModel>;
export declare namespace PronunciationDictionaryPhonemeRuleRequestModel {
    interface Raw {
        string_to_replace: string;
        phoneme: string;
        alphabet: string;
    }
}
