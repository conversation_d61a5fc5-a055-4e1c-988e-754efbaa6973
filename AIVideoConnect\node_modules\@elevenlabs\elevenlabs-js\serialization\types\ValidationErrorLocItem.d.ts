/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ValidationErrorLocItem: core.serialization.Schema<serializers.ValidationErrorLocItem.Raw, ElevenLabs.ValidationErrorLocItem>;
export declare namespace ValidationErrorLocItem {
    type Raw = string | number;
}
