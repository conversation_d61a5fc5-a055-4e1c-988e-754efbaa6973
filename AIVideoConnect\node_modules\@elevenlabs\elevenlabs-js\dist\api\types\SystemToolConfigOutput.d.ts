/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * A system tool is a tool that is used to call a system method in the server
 */
export interface SystemToolConfigOutput {
    name: string;
    description: string;
    /** The maximum time in seconds to wait for the tool call to complete. */
    responseTimeoutSecs?: number;
    /** If true, the user will not be able to interrupt the agent while this tool is running. */
    disableInterruptions?: boolean;
    /** If true, the agent will speak before the tool call. */
    forcePreToolSpeech?: boolean;
    /** Configuration for extracting values from tool responses and assigning them to dynamic variables */
    assignments?: ElevenLabs.DynamicVariableAssignment[];
    params: ElevenLabs.SystemToolConfigOutputParams;
}
