/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PydanticPronunciationDictionaryVersionLocator: core.serialization.ObjectSchema<serializers.PydanticPronunciationDictionaryVersionLocator.Raw, ElevenLabs.PydanticPronunciationDictionaryVersionLocator>;
export declare namespace PydanticPronunciationDictionaryVersionLocator {
    interface Raw {
        pronunciation_dictionary_id: string;
        version_id?: string | null;
    }
}
