/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TransferToAgentToolResultSuccessModel: core.serialization.ObjectSchema<serializers.TransferToAgentToolResultSuccessModel.Raw, ElevenLabs.TransferToAgentToolResultSuccessModel>;
export declare namespace TransferToAgentToolResultSuccessModel {
    interface Raw {
        status?: "success" | null;
        from_agent: string;
        to_agent: string;
        condition: string;
        delay_ms?: number | null;
        transfer_message?: string | null;
        enable_transferred_agent_first_message?: boolean | null;
    }
}
