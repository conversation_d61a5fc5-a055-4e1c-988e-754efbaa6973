/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ProjectSnapshotResponse } from "./ProjectSnapshotResponse";
export declare const ProjectSnapshotsResponse: core.serialization.ObjectSchema<serializers.ProjectSnapshotsResponse.Raw, ElevenLabs.ProjectSnapshotsResponse>;
export declare namespace ProjectSnapshotsResponse {
    interface Raw {
        snapshots: ProjectSnapshotResponse.Raw[];
    }
}
