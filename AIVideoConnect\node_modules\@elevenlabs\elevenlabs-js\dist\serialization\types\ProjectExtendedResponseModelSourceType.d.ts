/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectExtendedResponseModelSourceType: core.serialization.Schema<serializers.ProjectExtendedResponseModelSourceType.Raw, ElevenLabs.ProjectExtendedResponseModelSourceType>;
export declare namespace ProjectExtendedResponseModelSourceType {
    type Raw = "blank" | "book" | "article" | "genfm" | "video";
}
