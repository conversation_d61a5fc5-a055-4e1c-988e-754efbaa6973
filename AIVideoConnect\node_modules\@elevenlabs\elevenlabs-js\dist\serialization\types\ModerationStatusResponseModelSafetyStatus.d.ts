/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ModerationStatusResponseModelSafetyStatus: core.serialization.Schema<serializers.ModerationStatusResponseModelSafetyStatus.Raw, ElevenLabs.ModerationStatusResponseModelSafetyStatus>;
export declare namespace ModerationStatusResponseModelSafetyStatus {
    type Raw = "appeal_approved" | "appeal_denied" | "false_positive";
}
