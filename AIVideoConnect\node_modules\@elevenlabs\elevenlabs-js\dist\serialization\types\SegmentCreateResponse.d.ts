/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentCreateResponse: core.serialization.ObjectSchema<serializers.SegmentCreateResponse.Raw, ElevenLabs.SegmentCreateResponse>;
export declare namespace SegmentCreateResponse {
    interface Raw {
        version: number;
        new_segment: string;
    }
}
