import { sql } from "drizzle-orm";
import { pgTable, text, varchar, integer, jsonb, timestamp, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email"),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const assessmentSessions = pgTable("assessment_sessions", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").references(() => users.id),
  status: text("status").notNull().default("active"), // active, completed, cancelled
  phase: text("phase").notNull().default("welcome"), // welcome, discovery, movement, photo, reveal, complete
  personality: text("personality").default("personality1"), // personality1, personality2
  intake: jsonb("intake"),
  signals: jsonb("signals"),
  scores: jsonb("scores"),
  plan: jsonb("plan"),
  gasScore: integer("gas_score"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
});

export const workoutPlans = pgTable("workout_plans", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").references(() => users.id).notNull(),
  sessionId: varchar("session_id").references(() => assessmentSessions.id),
  planData: jsonb("plan_data").notNull(),
  isCoached: boolean("is_coached").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const sessionEvents = pgTable("session_events", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  sessionId: varchar("session_id").references(() => assessmentSessions.id).notNull(),
  eventType: text("event_type").notNull(), // question_answered, movement_detected, phase_changed, etc.
  eventData: jsonb("event_data"),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
});

export const insertAssessmentSessionSchema = createInsertSchema(assessmentSessions).pick({
  userId: true,
  status: true,
  phase: true,
});

export const insertWorkoutPlanSchema = createInsertSchema(workoutPlans).pick({
  userId: true,
  sessionId: true,
  planData: true,
  isCoached: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertAssessmentSession = z.infer<typeof insertAssessmentSessionSchema>;
export type AssessmentSession = typeof assessmentSessions.$inferSelect;
export type InsertWorkoutPlan = z.infer<typeof insertWorkoutPlanSchema>;
export type WorkoutPlan = typeof workoutPlans.$inferSelect;
export type SessionEvent = typeof sessionEvents.$inferSelect;
