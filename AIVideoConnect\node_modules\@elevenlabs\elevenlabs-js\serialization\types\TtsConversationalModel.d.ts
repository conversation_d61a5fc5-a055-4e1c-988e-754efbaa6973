/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TtsConversationalModel: core.serialization.Schema<serializers.TtsConversationalModel.Raw, ElevenLabs.TtsConversationalModel>;
export declare namespace TtsConversationalModel {
    type Raw = "eleven_turbo_v2" | "eleven_turbo_v2_5" | "eleven_flash_v2" | "eleven_flash_v2_5";
}
