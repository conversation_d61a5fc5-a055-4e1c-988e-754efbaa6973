/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface SubscriptionExtrasResponseModel {
    /** The concurrency of the user. */
    concurrency: number;
    /** The Convai concurrency of the user. */
    convaiConcurrency: number;
    /** The Convai characters per minute of the user. This field is deprecated and will always return None. */
    convaiCharsPerMinute?: number;
    /** The Convai ASR characters per minute of the user. This field is deprecated and will always return None. */
    convaiAsrCharsPerMinute?: number;
    /** Whether the user's logging is disabled. */
    forceLoggingDisabled: boolean;
    /** Whether the user can request manual pro voice verification. */
    canRequestManualProVoiceVerification: boolean;
    /** Whether the user can bypass the voice captcha. */
    canBypassVoiceCaptcha: boolean;
    /** The moderation status of the user. */
    moderation: ElevenLabs.ModerationStatusResponseModel;
    /** The unused characters rolled over from the previous period. */
    unusedCharactersRolledOverFromPreviousPeriod?: number;
    /** The overused characters rolled over from the previous period. */
    overusedCharactersRolledOverFromPreviousPeriod?: number;
    /** Data on how the subscription is being used. */
    usage?: ElevenLabs.SubscriptionUsageResponseModel;
}
