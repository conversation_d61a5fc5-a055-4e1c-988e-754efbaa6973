/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export type WorkflowPhoneNumberNodeModelTransferDestination = ElevenLabs.WorkflowPhoneNumberNodeModelTransferDestination.Phone | ElevenLabs.WorkflowPhoneNumberNodeModelTransferDestination.SipUri;
export declare namespace WorkflowPhoneNumberNodeModelTransferDestination {
    interface Phone extends ElevenLabs.PhoneNumberTransferDestination {
        type: "phone";
    }
    interface SipUri extends ElevenLabs.SipUriTransferDestination {
        type: "sip_uri";
    }
}
