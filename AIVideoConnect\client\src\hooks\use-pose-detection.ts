import { useState, useEffect, useRef, useCallback } from "react";
import { Pose } from "@mediapipe/pose";

export function usePoseDetection(videoRef: React.RefObject<HTMLVideoElement>) {
  const [poses, setPoses] = useState<any[]>([]);
  const [isDetecting, setIsDetecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const poseRef = useRef<Pose | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const connectWebSocket = useCallback(() => {
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const host = window.location.host || 'localhost:3000';
    const wsUrl = `${protocol}//${host}/ws`;

    console.log('Pose detection WebSocket connecting to:', wsUrl);
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;
    
    ws.onopen = () => {
      console.log('Pose detection WebSocket connected');
    };
    
    ws.onerror = (error) => {
      console.error('Pose detection WebSocket error:', error);
    };
  }, []);

  const initializePoseDetection = useCallback(async () => {
    try {
      const pose = new Pose({
        locateFile: (file) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`;
        }
      });

      pose.setOptions({
        modelComplexity: 1,
        smoothLandmarks: true,
        enableSegmentation: false,
        smoothSegmentation: false,
        minDetectionConfidence: 0.5,
        minTrackingConfidence: 0.5
      });

      pose.onResults((results) => {
        if (results.poseLandmarks) {
          const poseData = {
            landmarks: results.poseLandmarks,
            score: results.poseLandmarks.length > 0 ? 0.8 : 0.0,
            worldLandmarks: results.poseWorldLandmarks
          };

          setPoses([poseData]);

          // Send pose data to server via WebSocket
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'pose_data',
              poses: [poseData],
              timestamp: Date.now()
            }));
          }
        }
      });

      poseRef.current = pose;
    } catch (err) {
      console.error('Failed to initialize pose detection:', err);
      // Fallback to mock data if MediaPipe fails
      setError('Using fallback pose detection');
    }
  }, []);

  const detectPoses = useCallback(async () => {
    if (!videoRef.current || !isDetecting) return;

    try {
      if (poseRef.current) {
        // Use real MediaPipe detection
        await poseRef.current.send({ image: videoRef.current });
      } else {
        // Fallback to mock data
        const mockPose = {
          landmarks: Array(33).fill(null).map((_, i) => ({
            x: Math.random(),
            y: Math.random(),
            z: Math.random(),
            visibility: Math.random() > 0.3 ? 0.8 : 0.2
          })),
          score: Math.random() * 0.5 + 0.5,
        };

        setPoses([mockPose]);

        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({
            type: 'pose_data',
            poses: [mockPose],
            timestamp: Date.now()
          }));
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Pose detection failed');
    }
  }, [videoRef, isDetecting]);

  const startDetection = useCallback(async () => {
    if (isDetecting) return;

    setIsDetecting(true);
    setError(null);
    connectWebSocket();

    // Initialize MediaPipe pose detection
    await initializePoseDetection();

    // Start pose detection loop
    const interval = setInterval(detectPoses, 100); // 10 FPS
    return () => clearInterval(interval);
  }, [isDetecting, detectPoses, connectWebSocket, initializePoseDetection]);

  const stopDetection = useCallback(() => {
    if (!isDetecting) return;
    
    setIsDetecting(false);
    setPoses([]);
    
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  }, [isDetecting]);

  useEffect(() => {
    return () => {
      stopDetection();
    };
  }, [stopDetection]);

  return {
    poses,
    isDetecting,
    error,
    startDetection,
    stopDetection,
  };
}
