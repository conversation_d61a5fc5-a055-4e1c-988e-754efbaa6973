/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ObjectJsonSchemaPropertyInput: core.serialization.ObjectSchema<serializers.ObjectJsonSchemaPropertyInput.Raw, ElevenLabs.ObjectJsonSchemaPropertyInput>;
export declare namespace ObjectJsonSchemaPropertyInput {
    interface Raw {
        type?: "object" | null;
        required?: string[] | null;
        description?: string | null;
        properties?: Record<string, serializers.ObjectJsonSchemaPropertyInputPropertiesValue.Raw> | null;
    }
}
