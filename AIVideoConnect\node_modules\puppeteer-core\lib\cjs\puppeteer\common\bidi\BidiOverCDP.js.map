{"version": 3, "file": "BidiOverCDP.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/BidiOverCDP.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2FAA6E;AAO7E,mDAAiE;AAMjE;;GAEG;AACI,KAAK,UAAU,kBAAkB,CACtC,GAAsB;IAEtB,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG;QACpB,IAAI,CAAC,OAAe;YAClB,4EAA4E;YAC5E,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,KAAK;YACH,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;QACD,SAAS,CAAC,QAAgB;YACxB,8CAA8C;QAChD,CAAC;KACF,CAAC;IACF,aAAa,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,OAAe,EAAE,EAAE;QACnD,yDAAyD;QACzD,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IACH,MAAM,kBAAkB,GAAG,IAAI,0BAAkB,CAAC,aAAa,CAAC,CAAC;IACjE,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,cAAc,CAC3D,aAAa,EACb,oBAAoB,EACpB,EAAE,CACH,CAAC;IACF,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AA7BD,gDA6BC;AAED;;;GAGG;AACH,MAAM,oBAAoB;IAKxB,YAAY,GAAsB;QAJlC,4CAAwB;QACxB,yCAAY,IAAI,GAAG,EAA4C,EAAC;QAChE,gDAA8C;QAG5C,uBAAA,IAAI,6BAAQ,GAAG,MAAA,CAAC;QAChB,uBAAA,IAAI,iCAAY,IAAI,gBAAgB,CAAC,GAAG,CAAC,MAAA,CAAC;IAC5C,CAAC;IAED,aAAa;QACX,OAAO,uBAAA,IAAI,qCAAS,CAAC;IACvB,CAAC;IAED,YAAY,CAAC,EAAU;QACrB,MAAM,OAAO,GAAG,uBAAA,IAAI,iCAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,EAAE,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,uBAAA,IAAI,sCAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC9C,uBAAA,IAAI,sCAAU,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrC,OAAO,OAAO,CAAC;SAChB;QACD,OAAO,uBAAA,IAAI,sCAAU,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IACtC,CAAC;IAED,KAAK;QACH,uBAAA,IAAI,qCAAS,CAAC,KAAK,EAAE,CAAC;QACtB,KAAK,MAAM,OAAO,IAAI,uBAAA,IAAI,sCAAU,CAAC,MAAM,EAAE,EAAE;YAC7C,OAAO,CAAC,KAAK,EAAE,CAAC;SACjB;IACH,CAAC;CACF;;AAED;;;;;GAKG;AACH,MAAM,gBACJ,SAAQ,UAAU,CAAC,YAAuB;IAM1C,YAAY,MAAS;QACnB,KAAK,EAAE,CAAC;QAJV,mCAAU,KAAK,EAAC;QAChB,2CAAW;QAQX,2CAAkB,CAChB,MAAS,EACT,KAAmB,EACnB,EAAE;YACF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC3B,CAAC,EAAC;QATA,uBAAA,IAAI,4BAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,gCAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,uBAAA,IAAI,wCAAgC,CAAC,CAAC;IAC7D,CAAC;IASD,KAAK,CAAC,WAAW,CACf,MAAS,EACT,GAAG,MAAiD;QAEpD,IAAI,uBAAA,IAAI,gCAAQ,EAAE;YAChB,OAAO;SACR;QACD,IAAI;YACF,OAAO,MAAM,uBAAA,IAAI,gCAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;SACnD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,uBAAA,IAAI,gCAAQ,EAAE;gBAChB,OAAO;aACR;YACD,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAED,KAAK;QACH,uBAAA,IAAI,gCAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,uBAAA,IAAI,wCAAgC,CAAC,CAAC;QAC5D,uBAAA,IAAI,4BAAW,IAAI,MAAA,CAAC;IACtB,CAAC;CACF;;AAED;;;;GAIG;AACH,MAAM,aACJ,SAAQ,UAAU,CAAC,YAAiB;IADtC;;QAIE,mCAE4B,KAAK,EAC/B,EAAkC,EACnB,EAAE;YACjB,OAAO;QACT,CAAC,EAAC;IAuBJ,CAAC;IArBC,WAAW,CAAC,OAAuC;QACjD,uBAAA,IAAI,gCAAW,MAAf,IAAI,EAAY,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,YAAY,CACV,SAA4E;QAE5E,uBAAA,IAAI,4BAAc,SAAS,MAAA,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAqC;QACrD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,KAAK;QACH,uBAAA,IAAI,4BAAc,KAAK,EACrB,EAAkC,EACnB,EAAE;YACjB,OAAO;QACT,CAAC,MAAA,CAAC;IACJ,CAAC;CACF"}