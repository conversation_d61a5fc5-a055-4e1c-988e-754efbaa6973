/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PromptAgentApiModelOverride: core.serialization.ObjectSchema<serializers.PromptAgentApiModelOverride.Raw, ElevenLabs.PromptAgentApiModelOverride>;
export declare namespace PromptAgentApiModelOverride {
    interface Raw {
        prompt?: string | null;
        native_mcp_server_ids?: string[] | null;
    }
}
