/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectCreationMetaResponseModelStatus: core.serialization.Schema<serializers.ProjectCreationMetaResponseModelStatus.Raw, ElevenLabs.ProjectCreationMetaResponseModelStatus>;
export declare namespace ProjectCreationMetaResponseModelStatus {
    type Raw = "pending" | "creating" | "finished" | "failed";
}
