/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SafetyResponseModel: core.serialization.ObjectSchema<serializers.SafetyResponseModel.Raw, ElevenLabs.SafetyResponseModel>;
export declare namespace SafetyResponseModel {
    interface Raw {
        is_blocked_ivc?: boolean | null;
        is_blocked_non_ivc?: boolean | null;
        ignore_safety_evaluation?: boolean | null;
    }
}
