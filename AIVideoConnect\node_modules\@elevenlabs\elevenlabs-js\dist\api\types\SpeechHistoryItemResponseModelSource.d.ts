/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type SpeechHistoryItemResponseModelSource = "TTS" | "STS" | "Projects" | "PD" | "AN" | "Dubbing" | "PlayAPI" | "ConvAI";
export declare const SpeechHistoryItemResponseModelSource: {
    readonly Tts: "TTS";
    readonly Sts: "STS";
    readonly Projects: "Projects";
    readonly Pd: "PD";
    readonly An: "AN";
    readonly Dubbing: "Dubbing";
    readonly PlayApi: "PlayAPI";
    readonly ConvAi: "ConvAI";
};
