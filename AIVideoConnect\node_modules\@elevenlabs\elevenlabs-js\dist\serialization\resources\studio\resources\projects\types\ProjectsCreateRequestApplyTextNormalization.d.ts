/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const ProjectsCreateRequestApplyTextNormalization: core.serialization.Schema<serializers.studio.ProjectsCreateRequestApplyTextNormalization.Raw, ElevenLabs.studio.ProjectsCreateRequestApplyTextNormalization>;
export declare namespace ProjectsCreateRequestApplyTextNormalization {
    type Raw = "auto" | "on" | "off" | "apply_english";
}
