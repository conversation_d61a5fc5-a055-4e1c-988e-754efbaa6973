/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PendingSubscriptionSwitchResponseModelNextTier } from "./PendingSubscriptionSwitchResponseModelNextTier";
export declare const PendingSubscriptionSwitchResponseModel: core.serialization.ObjectSchema<serializers.PendingSubscriptionSwitchResponseModel.Raw, ElevenLabs.PendingSubscriptionSwitchResponseModel>;
export declare namespace PendingSubscriptionSwitchResponseModel {
    interface Raw {
        kind?: "change" | null;
        next_tier: PendingSubscriptionSwitchResponseModelNextTier.Raw;
        timestamp_seconds: number;
    }
}
