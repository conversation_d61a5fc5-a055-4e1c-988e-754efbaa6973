/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const ProjectsCreateRequestFiction: core.serialization.Schema<serializers.studio.ProjectsCreateRequestFiction.Raw, ElevenLabs.studio.ProjectsCreateRequestFiction>;
export declare namespace ProjectsCreateRequestFiction {
    type Raw = "fiction" | "non-fiction";
}
