/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentSubtitleFrame: core.serialization.ObjectSchema<serializers.SegmentSubtitleFrame.Raw, ElevenLabs.SegmentSubtitleFrame>;
export declare namespace SegmentSubtitleFrame {
    interface Raw {
        start_time: number;
        end_time: number;
        lines: string[];
    }
}
