/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ResourceAccessInfo } from "./ResourceAccessInfo";
export declare const UnitTestSummaryResponseModel: core.serialization.ObjectSchema<serializers.UnitTestSummaryResponseModel.Raw, ElevenLabs.UnitTestSummaryResponseModel>;
export declare namespace UnitTestSummaryResponseModel {
    interface Raw {
        id: string;
        name: string;
        access_info?: ResourceAccessInfo.Raw | null;
        created_at_unix_secs: number;
        last_updated_at_unix_secs: number;
    }
}
