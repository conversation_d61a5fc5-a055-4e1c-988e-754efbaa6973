/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
import { SingleTestRunRequestModel } from "../../../../../../types/SingleTestRunRequestModel";
import { AdhocAgentConfigOverrideForTestRequestModel } from "../../../../../../types/AdhocAgentConfigOverrideForTestRequestModel";
export declare const RunAgentTestsRequestModel: core.serialization.Schema<serializers.conversationalAi.RunAgentTestsRequestModel.Raw, ElevenLabs.conversationalAi.RunAgentTestsRequestModel>;
export declare namespace RunAgentTestsRequestModel {
    interface Raw {
        tests: SingleTestRunRequestModel.Raw[];
        agent_config_override?: AdhocAgentConfigOverrideForTestRequestModel.Raw | null;
    }
}
