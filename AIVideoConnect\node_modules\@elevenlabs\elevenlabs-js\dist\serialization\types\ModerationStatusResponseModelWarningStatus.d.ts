/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ModerationStatusResponseModelWarningStatus: core.serialization.Schema<serializers.ModerationStatusResponseModelWarningStatus.Raw, ElevenLabs.ModerationStatusResponseModelWarningStatus>;
export declare namespace ModerationStatusResponseModelWarningStatus {
    type Raw = "warning" | "warning_cleared";
}
