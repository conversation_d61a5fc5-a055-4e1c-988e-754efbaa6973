/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PostWorkspaceSecretResponseModel: core.serialization.ObjectSchema<serializers.PostWorkspaceSecretResponseModel.Raw, ElevenLabs.PostWorkspaceSecretResponseModel>;
export declare namespace PostWorkspaceSecretResponseModel {
    interface Raw {
        type: "stored";
        secret_id: string;
        name: string;
    }
}
