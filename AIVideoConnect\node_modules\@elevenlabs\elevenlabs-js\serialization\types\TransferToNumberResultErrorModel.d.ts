/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TransferToNumberResultErrorModel: core.serialization.ObjectSchema<serializers.TransferToNumberResultErrorModel.Raw, ElevenLabs.TransferToNumberResultErrorModel>;
export declare namespace TransferToNumberResultErrorModel {
    interface Raw {
        status?: "error" | null;
        error: string;
        details?: string | null;
    }
}
