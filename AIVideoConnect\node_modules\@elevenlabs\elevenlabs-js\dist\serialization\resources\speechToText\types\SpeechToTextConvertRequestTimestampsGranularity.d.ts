/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const SpeechToTextConvertRequestTimestampsGranularity: core.serialization.Schema<serializers.SpeechToTextConvertRequestTimestampsGranularity.Raw, ElevenLabs.SpeechToTextConvertRequestTimestampsGranularity>;
export declare namespace SpeechToTextConvertRequestTimestampsGranularity {
    type Raw = "none" | "word" | "character";
}
