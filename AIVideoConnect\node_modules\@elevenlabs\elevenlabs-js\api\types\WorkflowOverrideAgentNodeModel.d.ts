/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface WorkflowOverrideAgentNodeModel {
    conversationConfig?: ElevenLabs.ConversationalConfigApiModelWorkflowOverride;
    additionalPrompt?: string;
    additionalKnowledgeBase?: ElevenLabs.KnowledgeBaseLocator[];
    additionalToolIds?: string[];
    /** Position of the node in the workflow. */
    position?: ElevenLabs.Position;
    label: string;
}
