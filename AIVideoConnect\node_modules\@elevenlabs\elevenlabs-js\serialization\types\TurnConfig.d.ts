/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { TurnMode } from "./TurnMode";
export declare const TurnConfig: core.serialization.ObjectSchema<serializers.TurnConfig.Raw, ElevenLabs.TurnConfig>;
export declare namespace TurnConfig {
    interface Raw {
        turn_timeout?: number | null;
        silence_end_call_timeout?: number | null;
        mode?: TurnMode.Raw | null;
    }
}
