/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { Llm } from "./Llm";
import { BuiltInToolsWorkflowOverride } from "./BuiltInToolsWorkflowOverride";
import { KnowledgeBaseLocator } from "./KnowledgeBaseLocator";
import { CustomLlm } from "./CustomLlm";
import { RagConfigWorkflowOverride } from "./RagConfigWorkflowOverride";
import { PromptAgentApiModelWorkflowOverrideToolsItem } from "./PromptAgentApiModelWorkflowOverrideToolsItem";
export declare const PromptAgentApiModelWorkflowOverride: core.serialization.ObjectSchema<serializers.PromptAgentApiModelWorkflowOverride.Raw, ElevenLabs.PromptAgentApiModelWorkflowOverride>;
export declare namespace PromptAgentApiModelWorkflowOverride {
    interface Raw {
        prompt?: string | null;
        llm?: Llm.Raw | null;
        temperature?: number | null;
        max_tokens?: number | null;
        tool_ids?: string[] | null;
        built_in_tools?: BuiltInToolsWorkflowOverride.Raw | null;
        mcp_server_ids?: string[] | null;
        native_mcp_server_ids?: string[] | null;
        knowledge_base?: KnowledgeBaseLocator.Raw[] | null;
        custom_llm?: CustomLlm.Raw | null;
        ignore_default_personality?: boolean | null;
        rag?: RagConfigWorkflowOverride.Raw | null;
        timezone?: string | null;
        tools?: PromptAgentApiModelWorkflowOverrideToolsItem.Raw[] | null;
    }
}
