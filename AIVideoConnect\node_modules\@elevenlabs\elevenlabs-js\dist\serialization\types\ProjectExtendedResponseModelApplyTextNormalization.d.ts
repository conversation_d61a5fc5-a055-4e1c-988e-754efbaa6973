/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectExtendedResponseModelApplyTextNormalization: core.serialization.Schema<serializers.ProjectExtendedResponseModelApplyTextNormalization.Raw, ElevenLabs.ProjectExtendedResponseModelApplyTextNormalization>;
export declare namespace ProjectExtendedResponseModelApplyTextNormalization {
    type Raw = "auto" | "on" | "off" | "apply_english";
}
