/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { Llm } from "./Llm";
import { BuiltInToolsInput } from "./BuiltInToolsInput";
import { KnowledgeBaseLocator } from "./KnowledgeBaseLocator";
import { CustomLlm } from "./CustomLlm";
import { RagConfig } from "./RagConfig";
import { PromptAgentApiModelInputToolsItem } from "./PromptAgentApiModelInputToolsItem";
export declare const PromptAgentApiModelInput: core.serialization.ObjectSchema<serializers.PromptAgentApiModelInput.Raw, ElevenLabs.PromptAgentApiModelInput>;
export declare namespace PromptAgentApiModelInput {
    interface Raw {
        prompt?: string | null;
        llm?: Llm.Raw | null;
        temperature?: number | null;
        max_tokens?: number | null;
        tool_ids?: string[] | null;
        built_in_tools?: BuiltInToolsInput.Raw | null;
        mcp_server_ids?: string[] | null;
        native_mcp_server_ids?: string[] | null;
        knowledge_base?: KnowledgeBaseLocator.Raw[] | null;
        custom_llm?: CustomLlm.Raw | null;
        ignore_default_personality?: boolean | null;
        rag?: RagConfig.Raw | null;
        timezone?: string | null;
        tools?: PromptAgentApiModelInputToolsItem.Raw[] | null;
    }
}
