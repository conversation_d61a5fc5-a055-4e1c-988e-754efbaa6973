/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PronunciationDictionaryLocator: core.serialization.ObjectSchema<serializers.PronunciationDictionaryLocator.Raw, ElevenLabs.PronunciationDictionaryLocator>;
export declare namespace PronunciationDictionaryLocator {
    interface Raw {
        pronunciation_dictionary_id: string;
        version_id: string;
    }
}
