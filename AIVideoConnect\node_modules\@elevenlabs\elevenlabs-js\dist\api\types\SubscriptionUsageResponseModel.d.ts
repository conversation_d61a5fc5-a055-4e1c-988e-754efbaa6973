/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface SubscriptionUsageResponseModel {
    /** The rollover credits quota. */
    rolloverCreditsQuota: number;
    /** The subscription cycle credits quota. */
    subscriptionCycleCreditsQuota: number;
    /** The manually gifted credits quota. */
    manuallyGiftedCreditsQuota: number;
    /** The rollover credits used. */
    rolloverCreditsUsed: number;
    /** The subscription cycle credits used. */
    subscriptionCycleCreditsUsed: number;
    /** The manually gifted credits used. */
    manuallyGiftedCreditsUsed: number;
    /** The paid usage based credits used. */
    paidUsageBasedCreditsUsed: number;
    /** The actual reported credits. */
    actualReportedCredits: number;
}
