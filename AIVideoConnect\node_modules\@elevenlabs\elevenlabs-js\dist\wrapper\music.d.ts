import { Music as GeneratedMusic } from "../api/resources/music/client/Client";
import type * as ElevenLabs from "../api";
import type { CompositionPlan } from "../api/resources/music/resources/compositionPlan/client/Client";
export declare namespace Music {
    interface Options extends GeneratedMusic.Options {
    }
    interface RequestOptions extends GeneratedMusic.RequestOptions {
    }
}
export interface SongMetadata {
    title: string;
    description: string;
    genres: string[];
    languages: string[];
    is_explicit: boolean;
}
export interface MultipartResponse {
    json: {
        compositionPlan: CompositionPlan;
        songMetadata: SongMetadata;
    };
    audio: Buffer;
    filename: string;
}
export declare class Music extends GeneratedMusic {
    constructor(options?: Music.Options);
    /**
     * Compose a song from a prompt or a composition plan with detailed response parsing.
     * This method calls the original composeDetailed and then parses the stream response.
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     */
    composeDetailed(request?: ElevenLabs.BodyComposeMusicWithADetailedResponseV1MusicDetailedPost, requestOptions?: Music.RequestOptions): Promise<MultipartResponse>;
    /**
     * Reads a ReadableStream containing multipart data and parses it into JSON and audio parts
     * @param stream - ReadableStream from ElevenLabs music API response
     * @returns Object containing parsed JSON metadata, audio Buffer, and filename
     */
    private parseMultipart;
    /**
     * Converts snake_case keys to camelCase recursively
     */
    private toCamelCase;
}
