/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { TurnMode } from "./TurnMode";
export declare const TurnConfigWorkflowOverride: core.serialization.ObjectSchema<serializers.TurnConfigWorkflowOverride.Raw, ElevenLabs.TurnConfigWorkflowOverride>;
export declare namespace TurnConfigWorkflowOverride {
    interface Raw {
        turn_timeout?: number | null;
        silence_end_call_timeout?: number | null;
        mode?: TurnMode.Raw | null;
    }
}
