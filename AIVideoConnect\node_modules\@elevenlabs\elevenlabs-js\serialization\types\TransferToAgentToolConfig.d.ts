/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { AgentTransfer } from "./AgentTransfer";
export declare const TransferToAgentToolConfig: core.serialization.ObjectSchema<serializers.TransferToAgentToolConfig.Raw, ElevenLabs.TransferToAgentToolConfig>;
export declare namespace TransferToAgentToolConfig {
    interface Raw {
        transfers: AgentTransfer.Raw[];
    }
}
