/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectState: core.serialization.Schema<serializers.ProjectState.Raw, ElevenLabs.ProjectState>;
export declare namespace ProjectState {
    type Raw = "creating" | "default" | "converting" | "in_queue";
}
