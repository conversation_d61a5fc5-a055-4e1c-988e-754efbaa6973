/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ResourceMetadataResponseModelAnonymousAccessLevelOverride: core.serialization.Schema<serializers.ResourceMetadataResponseModelAnonymousAccessLevelOverride.Raw, ElevenLabs.ResourceMetadataResponseModelAnonymousAccessLevelOverride>;
export declare namespace ResourceMetadataResponseModelAnonymousAccessLevelOverride {
    type Raw = "admin" | "editor" | "viewer";
}
