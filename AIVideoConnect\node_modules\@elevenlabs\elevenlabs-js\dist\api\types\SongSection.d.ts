/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface SongSection {
    /** The name of the section. Must be between 1 and 100 characters. */
    sectionName: string;
    /** The styles that should be present in this section. */
    positiveLocalStyles: string[];
    /** The styles that should not be present in this section. */
    negativeLocalStyles: string[];
    /** The duration of the section in milliseconds. Must be between 3000ms and 120000ms. */
    durationMs: number;
    /** The lyrics of the section. */
    lines: string[];
}
