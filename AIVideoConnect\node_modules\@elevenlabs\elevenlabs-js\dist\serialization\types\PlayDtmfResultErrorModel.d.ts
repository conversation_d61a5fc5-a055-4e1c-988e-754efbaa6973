/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PlayDtmfResultErrorModel: core.serialization.ObjectSchema<serializers.PlayDtmfResultErrorModel.Raw, ElevenLabs.PlayDtmfResultErrorModel>;
export declare namespace PlayDtmfResultErrorModel {
    interface Raw {
        status?: "error" | null;
        error: string;
        details?: string | null;
    }
}
