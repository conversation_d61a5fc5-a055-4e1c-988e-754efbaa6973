/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { CreateTwilioPhoneNumberRequest } from "../../../../../types/CreateTwilioPhoneNumberRequest";
import { CreateSipTrunkPhoneNumberRequest } from "../../../../../types/CreateSipTrunkPhoneNumberRequest";
export declare const PhoneNumbersCreateRequestBody: core.serialization.Schema<serializers.conversationalAi.PhoneNumbersCreateRequestBody.Raw, ElevenLabs.conversationalAi.PhoneNumbersCreateRequestBody>;
export declare namespace PhoneNumbersCreateRequestBody {
    type Raw = PhoneNumbersCreateRequestBody.Twilio | PhoneNumbersCreateRequestBody.SipTrunk;
    interface Twilio extends CreateTwilioPhoneNumberRequest.Raw {
        provider: "twilio";
    }
    interface SipTrunk extends CreateSipTrunkPhoneNumberRequest.Raw {
        provider: "sip_trunk";
    }
}
