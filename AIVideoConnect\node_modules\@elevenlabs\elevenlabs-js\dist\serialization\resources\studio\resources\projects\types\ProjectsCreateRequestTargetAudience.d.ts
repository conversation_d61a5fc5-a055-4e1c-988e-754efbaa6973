/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const ProjectsCreateRequestTargetAudience: core.serialization.Schema<serializers.studio.ProjectsCreateRequestTargetAudience.Raw, ElevenLabs.studio.ProjectsCreateRequestTargetAudience>;
export declare namespace ProjectsCreateRequestTargetAudience {
    type Raw = "children" | "young adult" | "adult" | "all ages";
}
