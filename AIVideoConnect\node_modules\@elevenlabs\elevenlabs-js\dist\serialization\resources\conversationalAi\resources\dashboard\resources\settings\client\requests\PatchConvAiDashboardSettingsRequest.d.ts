/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../../../index";
import * as ElevenLabs from "../../../../../../../../../api/index";
import * as core from "../../../../../../../../../core";
import { PatchConvAiDashboardSettingsRequestChartsItem } from "../../types/PatchConvAiDashboardSettingsRequestChartsItem";
export declare const PatchConvAiDashboardSettingsRequest: core.serialization.Schema<serializers.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequest.Raw, ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequest>;
export declare namespace PatchConvAiDashboardSettingsRequest {
    interface Raw {
        charts?: PatchConvAiDashboardSettingsRequestChartsItem.Raw[] | null;
    }
}
