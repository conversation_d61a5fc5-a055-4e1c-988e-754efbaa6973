/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SongSection } from "./SongSection";
export declare const MusicPrompt: core.serialization.ObjectSchema<serializers.MusicPrompt.Raw, ElevenLabs.MusicPrompt>;
export declare namespace MusicPrompt {
    interface Raw {
        positive_global_styles: string[];
        negative_global_styles: string[];
        sections: SongSection.Raw[];
    }
}
