/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ToolUsageStatsResponseModel: core.serialization.ObjectSchema<serializers.ToolUsageStatsResponseModel.Raw, ElevenLabs.ToolUsageStatsResponseModel>;
export declare namespace ToolUsageStatsResponseModel {
    interface Raw {
        total_calls?: number | null;
        avg_latency_secs: number;
    }
}
