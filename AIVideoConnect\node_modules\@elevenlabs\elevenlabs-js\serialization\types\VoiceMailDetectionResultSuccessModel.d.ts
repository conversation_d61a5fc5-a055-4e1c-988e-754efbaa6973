/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceMailDetectionResultSuccessModel: core.serialization.ObjectSchema<serializers.VoiceMailDetectionResultSuccessModel.Raw, ElevenLabs.VoiceMailDetectionResultSuccessModel>;
export declare namespace VoiceMailDetectionResultSuccessModel {
    interface Raw {
        status?: "success" | null;
        voicemail_message?: string | null;
        reason?: string | null;
    }
}
