/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type WorkflowEdgeModelBackwardCondition = ElevenLabs.WorkflowEdgeModelBackwardCondition.Llm | ElevenLabs.WorkflowEdgeModelBackwardCondition.Result | ElevenLabs.WorkflowEdgeModelBackwardCondition.Unconditional;
export declare namespace WorkflowEdgeModelBackwardCondition {
    interface Llm extends ElevenLabs.WorkflowLlmConditionModel {
        type: "llm";
    }
    interface Result extends ElevenLabs.WorkflowResultConditionModel {
        type: "result";
    }
    interface Unconditional extends ElevenLabs.WorkflowUnconditionalModel {
        type: "unconditional";
    }
}
