/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PodcastBulletinModeData } from "./PodcastBulletinModeData";
export declare const PodcastBulletinMode: core.serialization.ObjectSchema<serializers.PodcastBulletinMode.Raw, ElevenLabs.PodcastBulletinMode>;
export declare namespace PodcastBulletinMode {
    interface Raw {
        bulletin: PodcastBulletinModeData.Raw;
    }
}
