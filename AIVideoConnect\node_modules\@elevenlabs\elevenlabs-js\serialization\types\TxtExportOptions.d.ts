/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TxtExportOptions: core.serialization.ObjectSchema<serializers.TxtExportOptions.Raw, ElevenLabs.TxtExportOptions>;
export declare namespace TxtExportOptions {
    interface Raw {
        max_characters_per_line?: number | null;
        include_speakers?: boolean | null;
        include_timestamps?: boolean | null;
        segment_on_silence_longer_than_s?: number | null;
        max_segment_duration_s?: number | null;
        max_segment_chars?: number | null;
    }
}
