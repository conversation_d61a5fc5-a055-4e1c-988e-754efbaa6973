/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../../../index";
import * as ElevenLabs from "../../../../../../../../../api/index";
import * as core from "../../../../../../../../../core";
import { McpApprovalPolicy } from "../../../../../../../../types/McpApprovalPolicy";
export declare const McpApprovalPolicyUpdateRequestModel: core.serialization.Schema<serializers.conversationalAi.mcpServers.McpApprovalPolicyUpdateRequestModel.Raw, ElevenLabs.conversationalAi.mcpServers.McpApprovalPolicyUpdateRequestModel>;
export declare namespace McpApprovalPolicyUpdateRequestModel {
    interface Raw {
        approval_policy: McpApprovalPolicy.Raw;
    }
}
