/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ClientToolConfigInput } from "./ClientToolConfigInput";
import { SystemToolConfigInput } from "./SystemToolConfigInput";
import { WebhookToolConfigInput } from "./WebhookToolConfigInput";
export declare const ToolRequestModelToolConfig: core.serialization.Schema<serializers.ToolRequestModelToolConfig.Raw, ElevenLabs.ToolRequestModelToolConfig>;
export declare namespace ToolRequestModelToolConfig {
    type Raw = ToolRequestModelToolConfig.Client | ToolRequestModelToolConfig.Mcp | ToolRequestModelToolConfig.System | ToolRequestModelToolConfig.Webhook;
    interface Client extends ClientToolConfigInput.Raw {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends SystemToolConfigInput.Raw {
        type: "system";
    }
    interface Webhook extends WebhookToolConfigInput.Raw {
        type: "webhook";
    }
}
