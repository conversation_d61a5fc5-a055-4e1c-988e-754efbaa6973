/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { CharacterAlignmentModel } from "./CharacterAlignmentModel";
export declare const ProjectSnapshotExtendedResponseModel: core.serialization.ObjectSchema<serializers.ProjectSnapshotExtendedResponseModel.Raw, ElevenLabs.ProjectSnapshotExtendedResponseModel>;
export declare namespace ProjectSnapshotExtendedResponseModel {
    interface Raw {
        project_snapshot_id: string;
        project_id: string;
        created_at_unix: number;
        name: string;
        audio_upload?: Record<string, unknown> | null;
        zip_upload?: Record<string, unknown> | null;
        character_alignments: CharacterAlignmentModel.Raw[];
    }
}
