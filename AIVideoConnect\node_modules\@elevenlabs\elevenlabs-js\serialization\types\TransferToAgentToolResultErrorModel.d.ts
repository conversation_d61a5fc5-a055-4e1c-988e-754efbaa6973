/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TransferToAgentToolResultErrorModel: core.serialization.ObjectSchema<serializers.TransferToAgentToolResultErrorModel.Raw, ElevenLabs.TransferToAgentToolResultErrorModel>;
export declare namespace TransferToAgentToolResultErrorModel {
    interface Raw {
        status?: "error" | null;
        from_agent: string;
        error: string;
    }
}
