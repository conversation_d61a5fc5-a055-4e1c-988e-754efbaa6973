/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface RecordingResponse {
    /** The ID of the recording. */
    recordingId: string;
    /** The MIME type of the recording. */
    mimeType: string;
    /** The size of the recording in bytes. */
    sizeBytes: number;
    /** The date of the recording in Unix time. */
    uploadDateUnix: number;
    /** The transcription of the recording. */
    transcription: string;
}
