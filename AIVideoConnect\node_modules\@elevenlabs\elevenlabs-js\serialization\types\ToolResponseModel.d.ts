/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ToolResponseModelToolConfig } from "./ToolResponseModelToolConfig";
import { ResourceAccessInfo } from "./ResourceAccessInfo";
import { ToolUsageStatsResponseModel } from "./ToolUsageStatsResponseModel";
export declare const ToolResponseModel: core.serialization.ObjectSchema<serializers.ToolResponseModel.Raw, ElevenLabs.ToolResponseModel>;
export declare namespace ToolResponseModel {
    interface Raw {
        id: string;
        tool_config: ToolResponseModelToolConfig.Raw;
        access_info: ResourceAccessInfo.Raw;
        usage_stats: ToolUsageStatsResponseModel.Raw;
    }
}
