/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * The type of tool
 */
export type PromptAgentApiModelWorkflowOverrideToolsItem = ElevenLabs.PromptAgentApiModelWorkflowOverrideToolsItem.Client | ElevenLabs.PromptAgentApiModelWorkflowOverrideToolsItem.Mcp | ElevenLabs.PromptAgentApiModelWorkflowOverrideToolsItem.System | ElevenLabs.PromptAgentApiModelWorkflowOverrideToolsItem.Webhook;
export declare namespace PromptAgentApiModelWorkflowOverrideToolsItem {
    interface Client extends ElevenLabs.ClientToolConfigInput {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends ElevenLabs.SystemToolConfigInput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigInput {
        type: "webhook";
    }
}
