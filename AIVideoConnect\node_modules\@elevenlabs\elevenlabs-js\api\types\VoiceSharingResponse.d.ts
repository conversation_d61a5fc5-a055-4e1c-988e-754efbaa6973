/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface VoiceSharingResponse {
    /** The status of the voice sharing. */
    status?: ElevenLabs.VoiceSharingState;
    /** The sample ID of the history item. */
    historyItemSampleId?: string;
    /** The date of the voice sharing in Unix time. */
    dateUnix?: number;
    /** A list of whitelisted emails. */
    whitelistedEmails?: string[];
    /** The ID of the public owner. */
    publicOwnerId?: string;
    /** The ID of the original voice. */
    originalVoiceId?: string;
    /** Whether financial rewards are enabled. */
    financialRewardsEnabled?: boolean;
    /** Whether free users are allowed. */
    freeUsersAllowed?: boolean;
    /** Whether live moderation is enabled. */
    liveModerationEnabled?: boolean;
    /** The rate of the voice sharing. */
    rate?: number;
    /** The rate of the voice sharing in USD per 1000 credits. */
    fiatRate?: number;
    /** The notice period of the voice sharing. */
    noticePeriod?: number;
    /** The date of the voice sharing in Unix time. */
    disableAtUnix?: number;
    /** Whether voice mixing is allowed. */
    voiceMixingAllowed?: boolean;
    /** Whether the voice is featured. */
    featured?: boolean;
    /** The category of the voice. */
    category?: ElevenLabs.VoiceSharingResponseModelCategory;
    /** Whether the reader app is enabled. */
    readerAppEnabled?: boolean;
    /** The image URL of the voice. */
    imageUrl?: string;
    /** The ban reason of the voice. */
    banReason?: string;
    /** The number of likes on the voice. */
    likedByCount?: number;
    /** The number of clones on the voice. */
    clonedByCount?: number;
    /** The name of the voice. */
    name?: string;
    /** The description of the voice. */
    description?: string;
    /** The labels of the voice. */
    labels?: Record<string, string>;
    /** The review status of the voice. */
    reviewStatus?: ElevenLabs.ReviewStatus;
    /** The review message of the voice. */
    reviewMessage?: string;
    /** Whether the voice is enabled in the library. */
    enabledInLibrary?: boolean;
    /** The Instagram username of the voice. */
    instagramUsername?: string;
    /** The Twitter/X username of the voice. */
    twitterUsername?: string;
    /** The YouTube username of the voice. */
    youtubeUsername?: string;
    /** The TikTok username of the voice. */
    tiktokUsername?: string;
    /** The moderation check of the voice. */
    moderationCheck?: ElevenLabs.VoiceSharingModerationCheckResponseModel;
    /** The reader restricted on of the voice. */
    readerRestrictedOn?: ElevenLabs.ReaderResourceResponseModel[];
}
