import { useState, useCallback, useRef } from "react";

// Declare global speech recognition types
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export function useSpeech() {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [error, setError] = useState<string | null>(null);

  const recognitionRef = useRef<any>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const wsRef = useRef<WebSocket | null>(null);

  const connectWebSocket = useCallback(() => {
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const host = window.location.host || 'localhost:3000';
    const wsUrl = `${protocol}//${host}/ws`;

    console.log('Speech WebSocket connecting to:', wsUrl);
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;
    
    ws.onopen = () => {
      console.log('Speech WebSocket connected');
    };
    
    ws.onerror = (error) => {
      console.error('Speech WebSocket error:', error);
    };
  }, []);

  const startWhisperRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

        // Send audio to server for Whisper transcription
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          const arrayBuffer = await audioBlob.arrayBuffer();
          const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

          wsRef.current.send(JSON.stringify({
            type: 'whisper_audio',
            audio: base64Audio,
            timestamp: Date.now()
          }));
        }

        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();

      // Record for 3 seconds, then process
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
      }, 3000);

    } catch (err) {
      setError('Failed to start audio recording');
    }
  }, []);

  const startListening = useCallback(() => {
    console.log('Starting speech recognition...');

    // Try Whisper first, fallback to browser STT
    if (navigator.mediaDevices && MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
      console.log('Using Whisper transcription');
      setIsListening(true);
      setError(null);
      connectWebSocket();
      startWhisperRecording();
      return;
    }

    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const errorMsg = 'Speech recognition not supported in this browser';
      console.error(errorMsg);
      setError(errorMsg);
      return;
    }

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      
      console.log('Speech recognition configured:', {
        continuous: recognition.continuous,
        interimResults: recognition.interimResults,
        lang: recognition.lang
      });

      recognition.onstart = () => {
        console.log('Speech recognition started successfully');
        setIsListening(true);
        setError(null);
        connectWebSocket();
      };

      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        console.log('Speech recognition result event:', event.results.length, 'results');

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            console.log('Final transcript:', result[0].transcript);
          } else {
            interimTranscript += result[0].transcript;
            console.log('Interim transcript:', result[0].transcript);
          }
        }

        // Show interim results immediately for better user feedback
        if (interimTranscript) {
          console.log('Updating with interim results:', interimTranscript);
          setTranscript(prev => prev + ' [listening: ' + interimTranscript + ']');
        }

        if (finalTranscript) {
          console.log('Adding final transcript:', finalTranscript);
          setTranscript(prev => prev.replace(/\[listening:.*?\]/g, '') + '\n' + finalTranscript);
          
          // Send speech data to server
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            console.log('Sending speech data to server');
            wsRef.current.send(JSON.stringify({
              type: 'speech_data',
              transcript: finalTranscript,
              confidence: event.results[event.resultIndex]?.[0]?.confidence || 0.8,
              timestamp: Date.now()
            }));
          } else {
            console.log('WebSocket not ready, speech data not sent');
          }
        }
      };

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error, event);
        const errorMsg = `Speech recognition error: ${event.error}`;
        setError(errorMsg);
        setIsListening(false);
        
        // Handle specific error cases
        if (event.error === 'not-allowed') {
          setError('Microphone permission denied. Please allow microphone access and try again.');
        } else if (event.error === 'no-speech') {
          setError('No speech detected. Please try speaking closer to the microphone.');
        }
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        setIsListening(false);
        if (wsRef.current) {
          wsRef.current.close();
        }
      };

      recognitionRef.current = recognition;
      recognition.start();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start speech recognition');
    }
  }, [connectWebSocket]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    if (wsRef.current) {
      wsRef.current.close();
    }
    setIsListening(false);
  }, []);

  return {
    isListening,
    transcript,
    error,
    startListening,
    stopListening,
  };
}
