/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TtsConversationalConfigOverride: core.serialization.ObjectSchema<serializers.TtsConversationalConfigOverride.Raw, ElevenLabs.TtsConversationalConfigOverride>;
export declare namespace TtsConversationalConfigOverride {
    interface Raw {
        voice_id?: string | null;
        stability?: number | null;
        speed?: number | null;
        similarity_boost?: number | null;
    }
}
