/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type WorkflowToolResponseModelInputStepsItem = ElevenLabs.WorkflowToolResponseModelInputStepsItem.Edge | ElevenLabs.WorkflowToolResponseModelInputStepsItem.MaxIterationsExceeded | ElevenLabs.WorkflowToolResponseModelInputStepsItem.NestedTools;
export declare namespace WorkflowToolResponseModelInputStepsItem {
    interface Edge extends ElevenLabs.WorkflowToolEdgeStepModel {
        type: "edge";
    }
    interface MaxIterationsExceeded extends ElevenLabs.WorkflowToolMaxIterationsExceededStepModel {
        type: "max_iterations_exceeded";
    }
    interface NestedTools extends ElevenLabs.WorkflowToolNestedToolsStepModelInput {
        type: "nested_tools";
    }
}
