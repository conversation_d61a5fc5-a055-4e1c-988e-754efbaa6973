/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const SpeechToSpeechStreamRequestOutputFormat: core.serialization.Schema<serializers.SpeechToSpeechStreamRequestOutputFormat.Raw, ElevenLabs.SpeechToSpeechStreamRequestOutputFormat>;
export declare namespace SpeechToSpeechStreamRequestOutputFormat {
    type Raw = "mp3_22050_32" | "mp3_44100_32" | "mp3_44100_64" | "mp3_44100_96" | "mp3_44100_128" | "mp3_44100_192" | "pcm_8000" | "pcm_16000" | "pcm_22050" | "pcm_24000" | "pcm_44100" | "pcm_48000" | "ulaw_8000" | "alaw_8000" | "opus_48000_32" | "opus_48000_64" | "opus_48000_96" | "opus_48000_128" | "opus_48000_192";
}
