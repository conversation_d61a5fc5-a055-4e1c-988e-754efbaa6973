/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PodcastConversationModeData: core.serialization.ObjectSchema<serializers.PodcastConversationModeData.Raw, ElevenLabs.PodcastConversationModeData>;
export declare namespace PodcastConversationModeData {
    interface Raw {
        host_voice_id: string;
        guest_voice_id: string;
    }
}
