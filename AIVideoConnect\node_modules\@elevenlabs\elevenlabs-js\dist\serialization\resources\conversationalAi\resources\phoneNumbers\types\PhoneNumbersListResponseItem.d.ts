/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { GetPhoneNumberTwilioResponseModel } from "../../../../../types/GetPhoneNumberTwilioResponseModel";
import { GetPhoneNumberSipTrunkResponseModel } from "../../../../../types/GetPhoneNumberSipTrunkResponseModel";
export declare const PhoneNumbersListResponseItem: core.serialization.Schema<serializers.conversationalAi.PhoneNumbersListResponseItem.Raw, ElevenLabs.conversationalAi.PhoneNumbersListResponseItem>;
export declare namespace PhoneNumbersListResponseItem {
    type Raw = PhoneNumbersListResponseItem.Twilio | PhoneNumbersListResponseItem.SipTrunk;
    interface Twilio extends GetPhoneNumberTwilioResponseModel.Raw {
        provider: "twilio";
    }
    interface SipTrunk extends GetPhoneNumberSipTrunkResponseModel.Raw {
        provider: "sip_trunk";
    }
}
