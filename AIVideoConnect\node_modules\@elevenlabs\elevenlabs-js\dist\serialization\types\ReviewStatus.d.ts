/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ReviewStatus: core.serialization.Schema<serializers.ReviewStatus.Raw, ElevenLabs.ReviewStatus>;
export declare namespace ReviewStatus {
    type Raw = "not_requested" | "pending" | "declined" | "allowed" | "allowed_with_changes";
}
