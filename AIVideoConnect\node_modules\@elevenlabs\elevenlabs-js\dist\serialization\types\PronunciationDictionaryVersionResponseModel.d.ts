/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PronunciationDictionaryVersionResponseModelPermissionOnResource } from "./PronunciationDictionaryVersionResponseModelPermissionOnResource";
export declare const PronunciationDictionaryVersionResponseModel: core.serialization.ObjectSchema<serializers.PronunciationDictionaryVersionResponseModel.Raw, ElevenLabs.PronunciationDictionaryVersionResponseModel>;
export declare namespace PronunciationDictionaryVersionResponseModel {
    interface Raw {
        version_id: string;
        version_rules_num: number;
        pronunciation_dictionary_id: string;
        dictionary_name: string;
        version_name: string;
        permission_on_resource?: PronunciationDictionaryVersionResponseModelPermissionOnResource.Raw | null;
        created_by: string;
        creation_time_unix: number;
        archived_time_unix?: number | null;
    }
}
