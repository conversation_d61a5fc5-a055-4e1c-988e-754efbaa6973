/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PodcastTextSource: core.serialization.ObjectSchema<serializers.PodcastTextSource.Raw, ElevenLabs.PodcastTextSource>;
export declare namespace PodcastTextSource {
    interface Raw {
        text: string;
    }
}
