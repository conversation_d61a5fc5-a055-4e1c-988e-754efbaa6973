/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectCreationMetaResponseModelType: core.serialization.Schema<serializers.ProjectCreationMetaResponseModelType.Raw, ElevenLabs.ProjectCreationMetaResponseModelType>;
export declare namespace ProjectCreationMetaResponseModelType {
    type Raw = "blank" | "generate_podcast" | "auto_assign_voices";
}
