/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface PronunciationDictionaryVersionResponseModel {
    versionId: string;
    versionRulesNum: number;
    pronunciationDictionaryId: string;
    dictionaryName: string;
    versionName: string;
    permissionOnResource?: ElevenLabs.PronunciationDictionaryVersionResponseModelPermissionOnResource;
    createdBy: string;
    creationTimeUnix: number;
    archivedTimeUnix?: number;
}
