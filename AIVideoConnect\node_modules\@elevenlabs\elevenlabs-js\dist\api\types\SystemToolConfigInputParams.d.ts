/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type SystemToolConfigInputParams = ElevenLabs.SystemToolConfigInputParams.EndCall | ElevenLabs.SystemToolConfigInputParams.LanguageDetection | ElevenLabs.SystemToolConfigInputParams.PlayKeypadTouchTone | ElevenLabs.SystemToolConfigInputParams.SkipTurn | ElevenLabs.SystemToolConfigInputParams.TransferToAgent | ElevenLabs.SystemToolConfigInputParams.TransferToNumber | ElevenLabs.SystemToolConfigInputParams.VoicemailDetection;
export declare namespace SystemToolConfigInputParams {
    interface EndCall extends ElevenLabs.EndCallToolConfig {
        systemToolType: "end_call";
    }
    interface LanguageDetection extends ElevenLabs.LanguageDetectionToolConfig {
        systemToolType: "language_detection";
    }
    interface PlayKeypadTouchTone extends ElevenLabs.PlayDtmfToolConfig {
        systemToolType: "play_keypad_touch_tone";
    }
    interface SkipTurn extends ElevenLabs.SkipTurnToolConfig {
        systemToolType: "skip_turn";
    }
    interface TransferToAgent extends ElevenLabs.TransferToAgentToolConfig {
        systemToolType: "transfer_to_agent";
    }
    interface TransferToNumber extends ElevenLabs.TransferToNumberToolConfigInput {
        systemToolType: "transfer_to_number";
    }
    interface VoicemailDetection extends ElevenLabs.VoicemailDetectionToolConfig {
        systemToolType: "voicemail_detection";
    }
}
