/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type WorkflowEdgeModelForwardCondition = ElevenLabs.WorkflowEdgeModelForwardCondition.Llm | ElevenLabs.WorkflowEdgeModelForwardCondition.Result | ElevenLabs.WorkflowEdgeModelForwardCondition.Unconditional;
export declare namespace WorkflowEdgeModelForwardCondition {
    interface Llm extends ElevenLabs.WorkflowLlmConditionModel {
        type: "llm";
    }
    interface Result extends ElevenLabs.WorkflowResultConditionModel {
        type: "result";
    }
    interface Unconditional extends ElevenLabs.WorkflowUnconditionalModel {
        type: "unconditional";
    }
}
