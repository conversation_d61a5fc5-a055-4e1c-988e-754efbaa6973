/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PrivacyConfig: core.serialization.ObjectSchema<serializers.PrivacyConfig.Raw, ElevenLabs.PrivacyConfig>;
export declare namespace PrivacyConfig {
    interface Raw {
        record_voice?: boolean | null;
        retention_days?: number | null;
        delete_transcript_and_pii?: boolean | null;
        delete_audio?: boolean | null;
        apply_to_existing_conversations?: boolean | null;
        zero_retention_mode?: boolean | null;
    }
}
