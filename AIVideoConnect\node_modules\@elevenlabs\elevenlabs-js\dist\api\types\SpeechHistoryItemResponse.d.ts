/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface SpeechHistoryItemResponse {
    /** The ID of the history item. */
    historyItemId: string;
    /** The ID of the request. */
    requestId?: string;
    /** The ID of the voice used. */
    voiceId?: string;
    /** The ID of the model. */
    modelId?: string;
    /** The name of the voice. */
    voiceName?: string;
    /** The category of the voice. Either 'premade', 'cloned', 'generated' or 'professional'. */
    voiceCategory?: ElevenLabs.SpeechHistoryItemResponseModelVoiceCategory;
    /** The text used to generate the audio item. */
    text?: string;
    /** Unix timestamp of when the item was created. */
    dateUnix: number;
    /** The character count change from. */
    characterCountChangeFrom: number;
    /** The character count change to. */
    characterCountChangeTo: number;
    /** The content type of the generated item. */
    contentType: string;
    state?: unknown;
    /** The settings of the history item. */
    settings?: Record<string, unknown>;
    /** Feedback associated with the generated item. Returns null if no feedback has been provided. */
    feedback?: ElevenLabs.FeedbackItem;
    /** The ID of the share link. */
    shareLinkId?: string;
    /** The source of the history item. Either TTS (text to speech), STS (speech to text), AN (audio native), Projects, Dubbing, PlayAPI, PD (pronunciation dictionary) or ConvAI (Agents Platform). */
    source?: ElevenLabs.SpeechHistoryItemResponseModelSource;
    /** The alignments of the history item. */
    alignments?: ElevenLabs.HistoryAlignmentsResponseModel;
    /** The dialogue (voice and text pairs) used to generate the audio item. If this is set then the top level `text` and `voice_id` fields will be empty. */
    dialogue?: ElevenLabs.DialogueInputResponseModel[];
}
