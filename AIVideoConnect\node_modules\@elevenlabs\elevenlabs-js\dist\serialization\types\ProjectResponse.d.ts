/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ProjectResponseModelTargetAudience } from "./ProjectResponseModelTargetAudience";
import { ProjectState } from "./ProjectState";
import { ProjectResponseModelAccessLevel } from "./ProjectResponseModelAccessLevel";
import { ProjectResponseModelFiction } from "./ProjectResponseModelFiction";
import { ProjectCreationMetaResponseModel } from "./ProjectCreationMetaResponseModel";
import { ProjectResponseModelSourceType } from "./ProjectResponseModelSourceType";
import { CaptionStyleModel } from "./CaptionStyleModel";
export declare const ProjectResponse: core.serialization.ObjectSchema<serializers.ProjectResponse.Raw, ElevenLabs.ProjectResponse>;
export declare namespace ProjectResponse {
    interface Raw {
        project_id: string;
        name: string;
        create_date_unix: number;
        default_title_voice_id: string;
        default_paragraph_voice_id: string;
        default_model_id: string;
        last_conversion_date_unix?: number | null;
        can_be_downloaded: boolean;
        title?: string | null;
        author?: string | null;
        description?: string | null;
        genres?: string[] | null;
        cover_image_url?: string | null;
        target_audience?: ProjectResponseModelTargetAudience.Raw | null;
        language?: string | null;
        content_type?: string | null;
        original_publication_date?: string | null;
        mature_content?: boolean | null;
        isbn_number?: string | null;
        volume_normalization: boolean;
        state: ProjectState.Raw;
        access_level: ProjectResponseModelAccessLevel.Raw;
        fiction?: ProjectResponseModelFiction.Raw | null;
        quality_check_on: boolean;
        quality_check_on_when_bulk_convert: boolean;
        creation_meta?: ProjectCreationMetaResponseModel.Raw | null;
        source_type?: ProjectResponseModelSourceType.Raw | null;
        chapters_enabled?: boolean | null;
        captions_enabled?: boolean | null;
        caption_style?: CaptionStyleModel.Raw | null;
        public_share_id?: string | null;
    }
}
