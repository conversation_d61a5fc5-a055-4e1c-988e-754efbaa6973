/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const OrbAvatar: core.serialization.ObjectSchema<serializers.OrbAvatar.Raw, ElevenLabs.OrbAvatar>;
export declare namespace OrbAvatar {
    interface Raw {
        color_1?: string | null;
        color_2?: string | null;
    }
}
