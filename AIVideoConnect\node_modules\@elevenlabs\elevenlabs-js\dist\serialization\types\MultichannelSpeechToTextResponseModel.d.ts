/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SpeechToTextChunkResponseModel } from "./SpeechToTextChunkResponseModel";
export declare const MultichannelSpeechToTextResponseModel: core.serialization.ObjectSchema<serializers.MultichannelSpeechToTextResponseModel.Raw, ElevenLabs.MultichannelSpeechToTextResponseModel>;
export declare namespace MultichannelSpeechToTextResponseModel {
    interface Raw {
        transcripts: SpeechToTextChunkResponseModel.Raw[];
        transcription_id?: string | null;
    }
}
