/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SafetyRule } from "./SafetyRule";
export declare const SafetyEvaluation: core.serialization.ObjectSchema<serializers.SafetyEvaluation.Raw, ElevenLabs.SafetyEvaluation>;
export declare namespace SafetyEvaluation {
    interface Raw {
        is_unsafe?: boolean | null;
        llm_reason?: string | null;
        safety_prompt_version?: number | null;
        matched_rule_id?: SafetyRule.Raw[] | null;
    }
}
