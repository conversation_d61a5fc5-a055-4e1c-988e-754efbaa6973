/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PdfExportOptions: core.serialization.ObjectSchema<serializers.PdfExportOptions.Raw, ElevenLabs.PdfExportOptions>;
export declare namespace PdfExportOptions {
    interface Raw {
        include_speakers?: boolean | null;
        include_timestamps?: boolean | null;
        segment_on_silence_longer_than_s?: number | null;
        max_segment_duration_s?: number | null;
        max_segment_chars?: number | null;
    }
}
