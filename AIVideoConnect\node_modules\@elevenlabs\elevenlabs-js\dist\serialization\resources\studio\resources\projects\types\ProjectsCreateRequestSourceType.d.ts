/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const ProjectsCreateRequestSourceType: core.serialization.Schema<serializers.studio.ProjectsCreateRequestSourceType.Raw, ElevenLabs.studio.ProjectsCreateRequestSourceType>;
export declare namespace ProjectsCreateRequestSourceType {
    type Raw = "blank" | "book" | "article" | "genfm" | "video";
}
