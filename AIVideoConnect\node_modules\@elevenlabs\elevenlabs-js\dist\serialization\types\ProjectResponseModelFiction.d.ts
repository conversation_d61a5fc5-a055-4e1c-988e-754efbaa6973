/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectResponseModelFiction: core.serialization.Schema<serializers.ProjectResponseModelFiction.Raw, ElevenLabs.ProjectResponseModelFiction>;
export declare namespace ProjectResponseModelFiction {
    type Raw = "fiction" | "non-fiction";
}
