/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectExtendedResponseModelFiction: core.serialization.Schema<serializers.ProjectExtendedResponseModelFiction.Raw, ElevenLabs.ProjectExtendedResponseModelFiction>;
export declare namespace ProjectExtendedResponseModelFiction {
    type Raw = "fiction" | "non-fiction";
}
