/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { PhoneNumbersListResponseItem } from "../types/PhoneNumbersListResponseItem";
export declare const Response: core.serialization.Schema<serializers.conversationalAi.phoneNumbers.list.Response.Raw, ElevenLabs.conversationalAi.PhoneNumbersListResponseItem[]>;
export declare namespace Response {
    type Raw = PhoneNumbersListResponseItem.Raw[];
}
