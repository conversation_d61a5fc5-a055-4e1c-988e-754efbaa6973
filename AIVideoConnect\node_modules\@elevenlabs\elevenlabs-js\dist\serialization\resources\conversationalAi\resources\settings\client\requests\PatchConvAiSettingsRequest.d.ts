/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
import { ConversationInitiationClientDataWebhook } from "../../../../../../types/ConversationInitiationClientDataWebhook";
import { ConvAiWebhooks } from "../../../../../../types/ConvAiWebhooks";
export declare const PatchConvAiSettingsRequest: core.serialization.Schema<serializers.conversationalAi.PatchConvAiSettingsRequest.Raw, ElevenLabs.conversationalAi.PatchConvAiSettingsRequest>;
export declare namespace PatchConvAiSettingsRequest {
    interface Raw {
        conversation_initiation_client_data_webhook?: ConversationInitiationClientDataWebhook.Raw | null;
        webhooks?: ConvAiWebhooks.Raw | null;
        can_use_mcp_servers?: boolean | null;
        rag_retention_period_days?: number | null;
    }
}
