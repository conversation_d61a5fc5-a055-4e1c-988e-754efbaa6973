/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { LanguageResponse } from "./LanguageResponse";
import { ModelRatesResponseModel } from "./ModelRatesResponseModel";
import { ModelResponseModelConcurrencyGroup } from "./ModelResponseModelConcurrencyGroup";
export declare const Model: core.serialization.ObjectSchema<serializers.Model.Raw, ElevenLabs.Model>;
export declare namespace Model {
    interface Raw {
        model_id: string;
        name?: string | null;
        can_be_finetuned?: boolean | null;
        can_do_text_to_speech?: boolean | null;
        can_do_voice_conversion?: boolean | null;
        can_use_style?: boolean | null;
        can_use_speaker_boost?: boolean | null;
        serves_pro_voices?: boolean | null;
        token_cost_factor?: number | null;
        description?: string | null;
        requires_alpha_access?: boolean | null;
        max_characters_request_free_user?: number | null;
        max_characters_request_subscribed_user?: number | null;
        maximum_text_length_per_request?: number | null;
        languages?: LanguageResponse.Raw[] | null;
        model_rates?: ModelRatesResponseModel.Raw | null;
        concurrency_group?: ModelResponseModelConcurrencyGroup.Raw | null;
    }
}
