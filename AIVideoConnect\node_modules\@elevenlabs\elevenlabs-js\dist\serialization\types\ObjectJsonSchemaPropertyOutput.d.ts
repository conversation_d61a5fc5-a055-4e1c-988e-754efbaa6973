/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ObjectJsonSchemaPropertyOutput: core.serialization.ObjectSchema<serializers.ObjectJsonSchemaPropertyOutput.Raw, ElevenLabs.ObjectJsonSchemaPropertyOutput>;
export declare namespace ObjectJsonSchemaPropertyOutput {
    interface Raw {
        type?: "object" | null;
        required?: string[] | null;
        description?: string | null;
        properties?: Record<string, serializers.ObjectJsonSchemaPropertyOutputPropertiesValue.Raw> | null;
    }
}
