/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../../../index";
import * as ElevenLabs from "../../../../../../../../../api/index";
import * as core from "../../../../../../../../../core";
import { McpToolApprovalPolicy } from "../../../../../../../../types/McpToolApprovalPolicy";
export declare const McpToolAddApprovalRequestModel: core.serialization.Schema<serializers.conversationalAi.mcpServers.McpToolAddApprovalRequestModel.Raw, ElevenLabs.conversationalAi.mcpServers.McpToolAddApprovalRequestModel>;
export declare namespace McpToolAddApprovalRequestModel {
    interface Raw {
        tool_name: string;
        tool_description: string;
        input_schema?: Record<string, unknown> | null;
        approval_policy?: McpToolApprovalPolicy.Raw | null;
    }
}
