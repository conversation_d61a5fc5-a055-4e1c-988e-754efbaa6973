/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type SystemToolConfigOutputParams = ElevenLabs.SystemToolConfigOutputParams.EndCall | ElevenLabs.SystemToolConfigOutputParams.LanguageDetection | ElevenLabs.SystemToolConfigOutputParams.PlayKeypadTouchTone | ElevenLabs.SystemToolConfigOutputParams.SkipTurn | ElevenLabs.SystemToolConfigOutputParams.TransferToAgent | ElevenLabs.SystemToolConfigOutputParams.TransferToNumber | ElevenLabs.SystemToolConfigOutputParams.VoicemailDetection;
export declare namespace SystemToolConfigOutputParams {
    interface EndCall extends ElevenLabs.EndCallToolConfig {
        systemToolType: "end_call";
    }
    interface LanguageDetection extends ElevenLabs.LanguageDetectionToolConfig {
        systemToolType: "language_detection";
    }
    interface PlayKeypadTouchTone extends ElevenLabs.PlayDtmfToolConfig {
        systemToolType: "play_keypad_touch_tone";
    }
    interface SkipTurn extends ElevenLabs.SkipTurnToolConfig {
        systemToolType: "skip_turn";
    }
    interface TransferToAgent extends ElevenLabs.TransferToAgentToolConfig {
        systemToolType: "transfer_to_agent";
    }
    interface TransferToNumber extends ElevenLabs.TransferToNumberToolConfigOutput {
        systemToolType: "transfer_to_number";
    }
    interface VoicemailDetection extends ElevenLabs.VoicemailDetectionToolConfig {
        systemToolType: "voicemail_detection";
    }
}
