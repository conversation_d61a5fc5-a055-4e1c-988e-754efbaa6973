/**
 * This file was auto-generated by Fe<PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ClientToolConfigOutput } from "./ClientToolConfigOutput";
import { SystemToolConfigOutput } from "./SystemToolConfigOutput";
import { WebhookToolConfigOutput } from "./WebhookToolConfigOutput";
export declare const ToolResponseModelToolConfig: core.serialization.Schema<serializers.ToolResponseModelToolConfig.Raw, ElevenLabs.ToolResponseModelToolConfig>;
export declare namespace ToolResponseModelToolConfig {
    type Raw = ToolResponseModelToolConfig.Client | ToolResponseModelToolConfig.Mcp | ToolResponseModelToolConfig.System | ToolResponseModelToolConfig.Webhook;
    interface Client extends ClientToolConfigOutput.Raw {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends SystemToolConfigOutput.Raw {
        type: "system";
    }
    interface Webhook extends WebhookToolConfigOutput.Raw {
        type: "webhook";
    }
}
