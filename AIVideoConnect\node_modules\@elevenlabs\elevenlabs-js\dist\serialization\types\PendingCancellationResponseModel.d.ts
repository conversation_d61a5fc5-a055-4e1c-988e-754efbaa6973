/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PendingCancellationResponseModel: core.serialization.ObjectSchema<serializers.PendingCancellationResponseModel.Raw, ElevenLabs.PendingCancellationResponseModel>;
export declare namespace PendingCancellationResponseModel {
    interface Raw {
        kind?: "cancellation" | null;
        timestamp_seconds: number;
    }
}
