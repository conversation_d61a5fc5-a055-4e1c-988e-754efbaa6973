/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectExtendedResponseModelTargetAudience: core.serialization.Schema<serializers.ProjectExtendedResponseModelTargetAudience.Raw, ElevenLabs.ProjectExtendedResponseModelTargetAudience>;
export declare namespace ProjectExtendedResponseModelTargetAudience {
    type Raw = "children" | "young adult" | "adult" | "all ages";
}
