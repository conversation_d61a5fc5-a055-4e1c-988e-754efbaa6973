/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentDubResponse: core.serialization.ObjectSchema<serializers.SegmentDubResponse.Raw, ElevenLabs.SegmentDubResponse>;
export declare namespace SegmentDubResponse {
    interface Raw {
        version: number;
    }
}
