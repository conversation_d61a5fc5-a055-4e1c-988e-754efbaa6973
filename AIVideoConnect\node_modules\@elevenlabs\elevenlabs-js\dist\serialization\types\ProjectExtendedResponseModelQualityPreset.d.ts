/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectExtendedResponseModelQualityPreset: core.serialization.Schema<serializers.ProjectExtendedResponseModelQualityPreset.Raw, ElevenLabs.ProjectExtendedResponseModelQualityPreset>;
export declare namespace ProjectExtendedResponseModelQualityPreset {
    type Raw = "standard" | "high" | "highest" | "ultra" | "ultra_lossless";
}
