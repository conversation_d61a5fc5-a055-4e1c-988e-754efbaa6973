/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { LiteralJsonSchemaProperty } from "./LiteralJsonSchemaProperty";
export declare const ObjectJsonSchemaPropertyInputPropertiesValue: core.serialization.Schema<serializers.ObjectJsonSchemaPropertyInputPropertiesValue.Raw, ElevenLabs.ObjectJsonSchemaPropertyInputPropertiesValue>;
export declare namespace ObjectJsonSchemaPropertyInputPropertiesValue {
    type Raw = LiteralJsonSchemaProperty.Raw | serializers.ObjectJsonSchemaPropertyInput.Raw | serializers.ArrayJsonSchemaPropertyInput.Raw;
}
