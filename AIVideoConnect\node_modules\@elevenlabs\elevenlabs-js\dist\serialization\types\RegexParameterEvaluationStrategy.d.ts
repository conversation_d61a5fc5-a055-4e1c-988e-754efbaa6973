/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RegexParameterEvaluationStrategy: core.serialization.ObjectSchema<serializers.RegexParameterEvaluationStrategy.Raw, ElevenLabs.RegexParameterEvaluationStrategy>;
export declare namespace RegexParameterEvaluationStrategy {
    interface Raw {
        pattern: string;
    }
}
