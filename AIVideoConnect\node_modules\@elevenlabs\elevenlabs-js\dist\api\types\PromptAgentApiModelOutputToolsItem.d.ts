/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Eleven<PERSON>abs from "../index";
/**
 * The type of tool
 */
export type PromptAgentApiModelOutputToolsItem = ElevenLabs.PromptAgentApiModelOutputToolsItem.Client | ElevenLabs.PromptAgentApiModelOutputToolsItem.Mcp | ElevenLabs.PromptAgentApiModelOutputToolsItem.System | ElevenLabs.PromptAgentApiModelOutputToolsItem.Webhook;
export declare namespace PromptAgentApiModelOutputToolsItem {
    interface Client extends ElevenLabs.ClientToolConfigOutput {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends ElevenLabs.SystemToolConfigOutput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigOutput {
        type: "webhook";
    }
}
