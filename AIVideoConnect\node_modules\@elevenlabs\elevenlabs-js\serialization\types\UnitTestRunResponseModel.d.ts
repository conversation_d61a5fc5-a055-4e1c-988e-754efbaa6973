/**
 * This file was auto-generated by Fe<PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { TestRunStatus } from "./TestRunStatus";
import { ConversationHistoryTranscriptCommonModelOutput } from "./ConversationHistoryTranscriptCommonModelOutput";
import { TestConditionResultCommonModel } from "./TestConditionResultCommonModel";
import { TestRunMetadata } from "./TestRunMetadata";
export declare const UnitTestRunResponseModel: core.serialization.ObjectSchema<serializers.UnitTestRunResponseModel.Raw, ElevenLabs.UnitTestRunResponseModel>;
export declare namespace UnitTestRunResponseModel {
    interface Raw {
        test_run_id: string;
        test_invocation_id: string;
        agent_id: string;
        workflow_node_id?: string | null;
        status: TestRunStatus.Raw;
        agent_responses?: ConversationHistoryTranscriptCommonModelOutput.Raw[] | null;
        test_id: string;
        condition_result?: TestConditionResultCommonModel.Raw | null;
        last_updated_at_unix?: number | null;
        metadata?: TestRunMetadata.Raw | null;
    }
}
