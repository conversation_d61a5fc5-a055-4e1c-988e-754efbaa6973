/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectResponseModelTargetAudience: core.serialization.Schema<serializers.ProjectResponseModelTargetAudience.Raw, ElevenLabs.ProjectResponseModelTargetAudience>;
export declare namespace ProjectResponseModelTargetAudience {
    type Raw = "children" | "young adult" | "adult" | "all ages";
}
