/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectResponseModelSourceType: core.serialization.Schema<serializers.ProjectResponseModelSourceType.Raw, ElevenLabs.ProjectResponseModelSourceType>;
export declare namespace ProjectResponseModelSourceType {
    type Raw = "blank" | "book" | "article" | "genfm" | "video";
}
