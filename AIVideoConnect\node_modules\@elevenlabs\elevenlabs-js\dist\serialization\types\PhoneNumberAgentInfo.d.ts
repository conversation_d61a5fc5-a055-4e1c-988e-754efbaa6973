/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PhoneNumberAgentInfo: core.serialization.ObjectSchema<serializers.PhoneNumberAgentInfo.Raw, ElevenLabs.PhoneNumberAgentInfo>;
export declare namespace PhoneNumberAgentInfo {
    interface Raw {
        agent_id: string;
        agent_name: string;
    }
}
