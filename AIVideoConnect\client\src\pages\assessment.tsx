import { useEffect, useState, useRef } from "react";
import { useParams } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import type { AssessmentSession } from "@shared/schema";
import Video<PERSON>all from "@/components/video-call";
import TranscriptPanel from "@/components/transcript-panel";
import AssessmentResults from "@/components/assessment-results";
import { useWebRTC } from "@/hooks/use-webrtc";
import { useSpeech } from "@/hooks/use-speech";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export default function Assessment() {
  const { sessionId } = useParams();
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(sessionId || null);
  const [showTranscript, setShowTranscript] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const hasConnectedRef = useRef<string | null>(null);
  
  const { isConnected, localStream, remoteStream, connect, disconnect } = useWebRTC();
  const { isListening, transcript, error: speechError, startListening, stopListening } = useSpeech();

  // Fetch current session data
  const { data: session, isLoading } = useQuery({
    queryKey: ['/api/assessment', currentSessionId],
    enabled: !!currentSessionId,
  });

  // Start new assessment session
  const startAssessmentMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/assessment/start", {
        userId: "demo-user", // In real app, get from auth
      });
      return response.json();
    },
    onSuccess: (data) => {
      setCurrentSessionId(data.id);
      queryClient.invalidateQueries({ queryKey: ['/api/assessment'] });
    },
  });

  // Progress to next phase
  const nextPhaseMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", `/api/assessment/${currentSessionId}/next-phase`, {});
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/assessment', currentSessionId] });
    },
  });

  // Complete assessment
  const completeAssessmentMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", `/api/assessment/${currentSessionId}/complete`, {});
      return response.json();
    },
    onSuccess: () => {
      setShowResults(true);
      queryClient.invalidateQueries({ queryKey: ['/api/assessment', currentSessionId] });
    },
  });

  // Update personality
  const updatePersonalityMutation = useMutation({
    mutationFn: async (personality: string) => {
      const response = await apiRequest("PATCH", `/api/assessment/${currentSessionId}`, {
        personality,
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/assessment', currentSessionId] });
    },
  });

  const handleStartAssessment = () => {
    startAssessmentMutation.mutate();
  };

  const handleEndCall = () => {
    disconnect();
    stopListening();
    setCurrentSessionId(null);
    hasConnectedRef.current = null;
  };

  const handleNextPhase = () => {
    if (session?.phase === "reveal") {
      completeAssessmentMutation.mutate();
    } else {
      nextPhaseMutation.mutate();
    }
  };

  // Don't auto-connect - let user enable manually for permissions
  const handleEnableCamera = async () => {
    if (!currentSessionId) return;
    try {
      console.log('Starting camera connection...');
      await connect(currentSessionId);
      console.log('Camera connected successfully');
    } catch (error) {
      console.error('Failed to enable camera:', error);
      // You might want to show a toast or error message to the user here
    }
  };

  if (!currentSessionId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-card to-background flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <div className="mb-6">
              <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                <svg className="w-10 h-10 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                </svg>
              </div>
              <h1 className="text-2xl font-display font-bold mb-2">AI Fitness Assessment</h1>
              <p className="text-muted-foreground">Get your personalized training plan with live movement analysis</p>
            </div>
            
            <Button 
              onClick={handleStartAssessment}
              disabled={startAssessmentMutation.isPending}
              className="w-full"
              data-testid="button-start-assessment"
            >
              {startAssessmentMutation.isPending ? "Starting..." : "Start Assessment"}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-card to-background flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-card to-background">
      {/* Top Control Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-card/80 backdrop-blur-md border-b border-border">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-accent rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-muted-foreground">Live Assessment</span>
              <span className="text-sm text-accent font-medium" data-testid="text-session-time">
                {session?.createdAt ? new Date(session.createdAt).toLocaleTimeString() : "00:00"}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => isListening ? stopListening() : startListening()}
              data-testid="button-toggle-mic"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                <path d="M17.3 11c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
              </svg>
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowTranscript(!showTranscript)}
              data-testid="button-toggle-transcript"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </Button>
            
            <Button 
              variant="destructive" 
              size="sm"
              onClick={handleEndCall}
              data-testid="button-end-call"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.71.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.28 0-.53-.11-.71-.28-.79-.74-1.69-1.36-2.67-1.85-.33-.16-.56-.5-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
              </svg>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pt-20 pb-6 px-6 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <VideoCall 
            session={session}
            localStream={localStream}
            remoteStream={remoteStream}
            transcript={transcript}
            onPersonalityChange={updatePersonalityMutation.mutate}
          />
          
          {/* Camera and Mic Controls */}
          <div className="bg-card rounded-2xl border border-border p-4 mt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-sm font-medium text-muted-foreground">Assessment Controls:</div>
                {!localStream ? (
                  <Button 
                    variant="outline"
                    size="sm"
                    onClick={handleEnableCamera}
                    data-testid="button-enable-camera"
                  >
                    📹 Enable Camera
                  </Button>
                ) : (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-600 dark:text-green-400">Camera Active</span>
                  </div>
                )}
                
                <div className="flex flex-col items-center space-y-1">
                  <Button 
                    variant={isListening ? "destructive" : "outline"} 
                    size="sm"
                    onClick={() => isListening ? stopListening() : startListening()}
                    data-testid="button-toggle-microphone"
                    className="min-w-[120px]"
                  >
                    {isListening ? "🔴 Stop Mic" : "🎤 Enable Mic"}
                  </Button>
                  {speechError && (
                    <div className="text-xs text-red-500 max-w-[120px] text-center">
                      {speechError}
                    </div>
                  )}
                  {isListening && !speechError && (
                    <div className="text-xs text-green-600 dark:text-green-400 flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Listening...</span>
                    </div>
                  )}
                </div>
              </div>
              
              <Button 
                variant="ghost"
                size="sm"
                onClick={() => setShowTranscript(!showTranscript)}
                data-testid="button-toggle-transcript"
              >
                📝 {showTranscript ? "Hide" : "Show"} Transcript
              </Button>
            </div>
          </div>

          {/* Session Progress Footer */}
          <div className="bg-card rounded-2xl border border-border p-6 mt-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-4 mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center">
                      <svg className="w-5 h-5 text-accent-foreground" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                    </div>
                    <span className="font-medium">Phase Complete</span>
                  </div>
                  <div className="flex-1 h-2 bg-muted rounded-full">
                    <div 
                      className="h-2 bg-gradient-to-r from-accent to-primary rounded-full transition-all duration-1000" 
                      style={{ width: session?.phase === "complete" ? "100%" : "65%" }}
                    ></div>
                  </div>
                  <span className="text-sm text-muted-foreground" data-testid="text-progress">
                    {session?.phase === "complete" ? "100%" : "65%"} Complete
                  </span>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  Current Phase: <span className="text-foreground font-medium capitalize" data-testid="text-current-phase">
                    {session?.phase || "Loading..."}
                  </span>
                </div>
              </div>
              
              <Button 
                onClick={handleNextPhase}
                disabled={nextPhaseMutation.isPending || completeAssessmentMutation.isPending}
                data-testid="button-next-phase"
              >
                {nextPhaseMutation.isPending ? "Progressing..." : 
                 completeAssessmentMutation.isPending ? "Analyzing..." : 
                 session?.phase === "reveal" ? "Complete Assessment" : "Next Phase"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Transcript Panel */}
      <TranscriptPanel 
        isOpen={showTranscript}
        onClose={() => setShowTranscript(false)}
        transcript={transcript}
        session={session}
      />

      {/* Assessment Results Modal */}
      <AssessmentResults 
        isOpen={showResults}
        onClose={() => setShowResults(false)}
        session={session}
      />
    </div>
  );
}
