/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as ElevenLabs from "../../../../../api/index";
import * as core from "../../../../../core";
export declare const PatchWorkspaceSecretRequest: core.serialization.Schema<serializers.PatchWorkspaceSecretRequest.Raw, ElevenLabs.PatchWorkspaceSecretRequest>;
export declare namespace PatchWorkspaceSecretRequest {
    interface Raw {
        name: string;
        value: string;
    }
}
