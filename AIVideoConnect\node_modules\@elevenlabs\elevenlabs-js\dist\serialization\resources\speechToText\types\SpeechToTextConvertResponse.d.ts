/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
import { SpeechToTextChunkResponseModel } from "../../../types/SpeechToTextChunkResponseModel";
import { MultichannelSpeechToTextResponseModel } from "../../../types/MultichannelSpeechToTextResponseModel";
import { SpeechToTextWebhookResponseModel } from "../../../types/SpeechToTextWebhookResponseModel";
export declare const SpeechToTextConvertResponse: core.serialization.Schema<serializers.SpeechToTextConvertResponse.Raw, ElevenLabs.SpeechToTextConvertResponse>;
export declare namespace SpeechToTextConvertResponse {
    type Raw = SpeechToTextChunkResponseModel.Raw | MultichannelSpeechToTextResponseModel.Raw | SpeechToTextWebhookResponseModel.Raw;
}
