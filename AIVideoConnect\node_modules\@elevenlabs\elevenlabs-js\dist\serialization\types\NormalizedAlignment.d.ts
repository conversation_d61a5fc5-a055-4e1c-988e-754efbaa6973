/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const NormalizedAlignment: core.serialization.ObjectSchema<serializers.NormalizedAlignment.Raw, ElevenLabs.NormalizedAlignment>;
export declare namespace NormalizedAlignment {
    interface Raw {
        charStartTimesMs?: number[] | null;
        charsDurationsMs?: number[] | null;
        chars?: string[] | null;
    }
}
