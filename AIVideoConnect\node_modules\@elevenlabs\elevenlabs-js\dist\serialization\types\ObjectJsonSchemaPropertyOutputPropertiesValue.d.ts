/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { LiteralJsonSchemaProperty } from "./LiteralJsonSchemaProperty";
export declare const ObjectJsonSchemaPropertyOutputPropertiesValue: core.serialization.Schema<serializers.ObjectJsonSchemaPropertyOutputPropertiesValue.Raw, ElevenLabs.ObjectJsonSchemaPropertyOutputPropertiesValue>;
export declare namespace ObjectJsonSchemaPropertyOutputPropertiesValue {
    type Raw = LiteralJsonSchemaProperty.Raw | serializers.ObjectJsonSchemaPropertyOutput.Raw | serializers.ArrayJsonSchemaPropertyOutput.Raw;
}
