/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PromptAgentDbModel: core.serialization.ObjectSchema<serializers.PromptAgentDbModel.Raw, ElevenLabs.PromptAgentDbModel>;
export declare namespace PromptAgentDbModel {
    interface Raw {
        tools?: unknown | null;
    }
}
