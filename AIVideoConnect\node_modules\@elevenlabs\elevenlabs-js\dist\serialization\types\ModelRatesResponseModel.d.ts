/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ModelRatesResponseModel: core.serialization.ObjectSchema<serializers.ModelRatesResponseModel.Raw, ElevenLabs.ModelRatesResponseModel>;
export declare namespace ModelRatesResponseModel {
    interface Raw {
        character_cost_multiplier: number;
    }
}
