/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TransferToNumberResultSipSuccessModel: core.serialization.ObjectSchema<serializers.TransferToNumberResultSipSuccessModel.Raw, ElevenLabs.TransferToNumberResultSipSuccessModel>;
export declare namespace TransferToNumberResultSipSuccessModel {
    interface Raw {
        status?: "success" | null;
        transfer_number: string;
        reason?: string | null;
        note?: string | null;
    }
}
