/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
export declare const ListTestsByIdsRequestModel: core.serialization.Schema<serializers.conversationalAi.ListTestsByIdsRequestModel.Raw, ElevenLabs.conversationalAi.ListTestsByIdsRequestModel>;
export declare namespace ListTestsByIdsRequestModel {
    interface Raw {
        test_ids: string[];
    }
}
