/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../../index";
import * as ElevenLabs from "../../../../../../../../api/index";
import * as core from "../../../../../../../../core";
import { DashboardCallSuccessChartModel } from "../../../../../../../types/DashboardCallSuccessChartModel";
import { DashboardCriteriaChartModel } from "../../../../../../../types/DashboardCriteriaChartModel";
import { DashboardDataCollectionChartModel } from "../../../../../../../types/DashboardDataCollectionChartModel";
export declare const PatchConvAiDashboardSettingsRequestChartsItem: core.serialization.Schema<serializers.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequestChartsItem.Raw, ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequestChartsItem>;
export declare namespace PatchConvAiDashboardSettingsRequestChartsItem {
    type Raw = PatchConvAiDashboardSettingsRequestChartsItem.CallSuccess | PatchConvAiDashboardSettingsRequestChartsItem.Criteria | PatchConvAiDashboardSettingsRequestChartsItem.DataCollection;
    interface CallSuccess extends DashboardCallSuccessChartModel.Raw {
        type: "call_success";
    }
    interface Criteria extends DashboardCriteriaChartModel.Raw {
        type: "criteria";
    }
    interface DataCollection extends DashboardDataCollectionChartModel.Raw {
        type: "data_collection";
    }
}
