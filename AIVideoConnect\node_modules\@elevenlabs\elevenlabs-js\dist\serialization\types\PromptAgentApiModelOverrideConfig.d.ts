/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PromptAgentApiModelOverrideConfig: core.serialization.ObjectSchema<serializers.PromptAgentApiModelOverrideConfig.Raw, ElevenLabs.PromptAgentApiModelOverrideConfig>;
export declare namespace PromptAgentApiModelOverrideConfig {
    interface Raw {
        prompt?: boolean | null;
        native_mcp_server_ids?: boolean | null;
    }
}
