/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ModelSettingsResponseModel: core.serialization.ObjectSchema<serializers.ModelSettingsResponseModel.Raw, ElevenLabs.ModelSettingsResponseModel>;
export declare namespace ModelSettingsResponseModel {
    interface Raw {
        stability?: number | null;
        use_speaker_boost?: boolean | null;
    }
}
