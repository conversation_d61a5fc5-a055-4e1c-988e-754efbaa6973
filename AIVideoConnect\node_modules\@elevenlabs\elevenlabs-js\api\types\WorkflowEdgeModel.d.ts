/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface WorkflowEdgeModel {
    source: string;
    target: string;
    /** Condition that must be met for the edge to be traversed in the forward direction. */
    forwardCondition?: ElevenLabs.WorkflowEdgeModelForwardCondition;
    /** Condition that must be met for the edge to be traversed in the backward direction. */
    backwardCondition?: ElevenLabs.WorkflowEdgeModelBackwardCondition;
}
