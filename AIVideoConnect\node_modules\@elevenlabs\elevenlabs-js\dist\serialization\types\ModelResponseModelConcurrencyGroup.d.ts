/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ModelResponseModelConcurrencyGroup: core.serialization.Schema<serializers.ModelResponseModelConcurrencyGroup.Raw, ElevenLabs.ModelResponseModelConcurrencyGroup>;
export declare namespace ModelResponseModelConcurrencyGroup {
    type Raw = "standard" | "turbo";
}
