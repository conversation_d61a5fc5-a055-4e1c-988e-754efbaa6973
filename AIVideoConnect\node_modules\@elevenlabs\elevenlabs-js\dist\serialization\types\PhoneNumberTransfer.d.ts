/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PhoneNumberTransferTransferDestination } from "./PhoneNumberTransferTransferDestination";
import { TransferTypeEnum } from "./TransferTypeEnum";
export declare const PhoneNumberTransfer: core.serialization.ObjectSchema<serializers.PhoneNumberTransfer.Raw, ElevenLabs.PhoneNumberTransfer>;
export declare namespace PhoneNumberTransfer {
    interface Raw {
        transfer_destination?: PhoneNumberTransferTransferDestination.Raw | null;
        phone_number?: string | null;
        condition: string;
        transfer_type?: TransferTypeEnum.Raw | null;
    }
}
