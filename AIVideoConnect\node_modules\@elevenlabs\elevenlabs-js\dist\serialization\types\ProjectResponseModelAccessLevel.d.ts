/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectResponseModelAccessLevel: core.serialization.Schema<serializers.ProjectResponseModelAccessLevel.Raw, ElevenLabs.ProjectResponseModelAccessLevel>;
export declare namespace ProjectResponseModelAccessLevel {
    type Raw = "admin" | "editor" | "viewer";
}
