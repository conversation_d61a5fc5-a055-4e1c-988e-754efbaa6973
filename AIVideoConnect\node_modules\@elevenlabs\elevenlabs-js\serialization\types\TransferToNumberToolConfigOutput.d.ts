/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PhoneNumberTransfer } from "./PhoneNumberTransfer";
export declare const TransferToNumberToolConfigOutput: core.serialization.ObjectSchema<serializers.TransferToNumberToolConfigOutput.Raw, ElevenLabs.TransferToNumberToolConfigOutput>;
export declare namespace TransferToNumberToolConfigOutput {
    interface Raw {
        transfers: PhoneNumberTransfer.Raw[];
        enable_client_message?: boolean | null;
    }
}
