/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type WorkflowToolResponseModelOutputStepsItem = ElevenLabs.WorkflowToolResponseModelOutputStepsItem.Edge | ElevenLabs.WorkflowToolResponseModelOutputStepsItem.MaxIterationsExceeded | ElevenLabs.WorkflowToolResponseModelOutputStepsItem.NestedTools;
export declare namespace WorkflowToolResponseModelOutputStepsItem {
    interface Edge extends ElevenLabs.WorkflowToolEdgeStepModel {
        type: "edge";
    }
    interface MaxIterationsExceeded extends ElevenLabs.WorkflowToolMaxIterationsExceededStepModel {
        type: "max_iterations_exceeded";
    }
    interface NestedTools extends ElevenLabs.WorkflowToolNestedToolsStepModelOutput {
        type: "nested_tools";
    }
}
