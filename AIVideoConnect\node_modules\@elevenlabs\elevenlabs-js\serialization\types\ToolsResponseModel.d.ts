/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ToolResponseModel } from "./ToolResponseModel";
export declare const ToolsResponseModel: core.serialization.ObjectSchema<serializers.ToolsResponseModel.Raw, ElevenLabs.ToolsResponseModel>;
export declare namespace ToolsResponseModel {
    interface Raw {
        tools: ToolResponseModel.Raw[];
    }
}
