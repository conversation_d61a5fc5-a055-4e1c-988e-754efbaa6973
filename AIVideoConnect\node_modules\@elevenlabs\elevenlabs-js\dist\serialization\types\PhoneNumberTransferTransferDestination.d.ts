/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PhoneNumberTransferDestination } from "./PhoneNumberTransferDestination";
import { SipUriTransferDestination } from "./SipUriTransferDestination";
export declare const PhoneNumberTransferTransferDestination: core.serialization.Schema<serializers.PhoneNumberTransferTransferDestination.Raw, ElevenLabs.PhoneNumberTransferTransferDestination>;
export declare namespace PhoneNumberTransferTransferDestination {
    type Raw = PhoneNumberTransferTransferDestination.Phone | PhoneNumberTransferTransferDestination.SipUri;
    interface Phone extends PhoneNumberTransferDestination.Raw {
        type: "phone";
    }
    interface SipUri extends SipUriTransferDestination.Raw {
        type: "sip_uri";
    }
}
