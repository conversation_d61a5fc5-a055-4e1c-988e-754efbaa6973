/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ClientToolConfigOutput } from "./ClientToolConfigOutput";
import { SystemToolConfigOutput } from "./SystemToolConfigOutput";
import { WebhookToolConfigOutput } from "./WebhookToolConfigOutput";
export declare const PromptAgentApiModelOutputToolsItem: core.serialization.Schema<serializers.PromptAgentApiModelOutputToolsItem.Raw, ElevenLabs.PromptAgentApiModelOutputToolsItem>;
export declare namespace PromptAgentApiModelOutputToolsItem {
    type Raw = PromptAgentApiModelOutputToolsItem.Client | PromptAgentApiModelOutputToolsItem.Mcp | PromptAgentApiModelOutputToolsItem.System | PromptAgentApiModelOutputToolsItem.Webhook;
    interface Client extends ClientToolConfigOutput.Raw {
        type: "client";
    }
    interface Mcp {
        type: "mcp";
        value?: unknown;
    }
    interface System extends SystemToolConfigOutput.Raw {
        type: "system";
    }
    interface Webhook extends WebhookToolConfigOutput.Raw {
        type: "webhook";
    }
}
