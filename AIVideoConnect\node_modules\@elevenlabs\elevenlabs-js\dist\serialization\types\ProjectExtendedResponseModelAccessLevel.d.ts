/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ProjectExtendedResponseModelAccessLevel: core.serialization.Schema<serializers.ProjectExtendedResponseModelAccessLevel.Raw, ElevenLabs.ProjectExtendedResponseModelAccessLevel>;
export declare namespace ProjectExtendedResponseModelAccessLevel {
    type Raw = "admin" | "editor" | "viewer";
}
