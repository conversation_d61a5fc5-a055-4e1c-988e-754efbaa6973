/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { UnitTestToolCallParameterEval } from "./UnitTestToolCallParameterEval";
export declare const UnitTestToolCallParameter: core.serialization.ObjectSchema<serializers.UnitTestToolCallParameter.Raw, ElevenLabs.UnitTestToolCallParameter>;
export declare namespace UnitTestToolCallParameter {
    interface Raw {
        eval: UnitTestToolCallParameterEval.Raw;
        path: string;
    }
}
