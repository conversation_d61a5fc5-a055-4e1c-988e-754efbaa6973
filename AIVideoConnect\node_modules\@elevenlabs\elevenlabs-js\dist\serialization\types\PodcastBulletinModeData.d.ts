/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PodcastBulletinModeData: core.serialization.ObjectSchema<serializers.PodcastBulletinModeData.Raw, ElevenLabs.PodcastBulletinModeData>;
export declare namespace PodcastBulletinModeData {
    interface Raw {
        host_voice_id: string;
    }
}
