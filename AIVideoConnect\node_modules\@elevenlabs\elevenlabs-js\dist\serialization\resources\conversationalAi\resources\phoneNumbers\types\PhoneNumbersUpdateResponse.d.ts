/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { GetPhoneNumberTwilioResponseModel } from "../../../../../types/GetPhoneNumberTwilioResponseModel";
import { GetPhoneNumberSipTrunkResponseModel } from "../../../../../types/GetPhoneNumberSipTrunkResponseModel";
export declare const PhoneNumbersUpdateResponse: core.serialization.Schema<serializers.conversationalAi.PhoneNumbersUpdateResponse.Raw, ElevenLabs.conversationalAi.PhoneNumbersUpdateResponse>;
export declare namespace PhoneNumbersUpdateResponse {
    type Raw = PhoneNumbersUpdateResponse.Twilio | PhoneNumbersUpdateResponse.SipTrunk;
    interface Twilio extends GetPhoneNumberTwilioResponseModel.Raw {
        provider: "twilio";
    }
    interface SipTrunk extends GetPhoneNumberSipTrunkResponseModel.Raw {
        provider: "sip_trunk";
    }
}
