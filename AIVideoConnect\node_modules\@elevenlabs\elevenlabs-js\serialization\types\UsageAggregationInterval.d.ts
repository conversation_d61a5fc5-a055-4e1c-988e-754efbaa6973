/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const UsageAggregationInterval: core.serialization.Schema<serializers.UsageAggregationInterval.Raw, ElevenLabs.UsageAggregationInterval>;
export declare namespace UsageAggregationInterval {
    type Raw = "hour" | "day" | "week" | "month" | "cumulative";
}
