/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TransferToNumberResultTwilioSuccessModel: core.serialization.ObjectSchema<serializers.TransferToNumberResultTwilioSuccessModel.Raw, ElevenLabs.TransferToNumberResultTwilioSuccessModel>;
export declare namespace TransferToNumberResultTwilioSuccessModel {
    interface Raw {
        status?: "success" | null;
        transfer_number: string;
        reason?: string | null;
        client_message?: string | null;
        agent_message: string;
        conference_name: string;
        note?: string | null;
    }
}
