/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { UnitTestToolCallParameter } from "./UnitTestToolCallParameter";
import { ReferencedToolCommonModel } from "./ReferencedToolCommonModel";
export declare const UnitTestToolCallEvaluationModelOutput: core.serialization.ObjectSchema<serializers.UnitTestToolCallEvaluationModelOutput.Raw, ElevenLabs.UnitTestToolCallEvaluationModelOutput>;
export declare namespace UnitTestToolCallEvaluationModelOutput {
    interface Raw {
        parameters?: UnitTestToolCallParameter.Raw[] | null;
        referenced_tool: ReferencedToolCommonModel.Raw;
    }
}
