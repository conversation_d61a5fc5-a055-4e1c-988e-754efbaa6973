/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PronunciationDictionaryVersionLocator: core.serialization.ObjectSchema<serializers.PronunciationDictionaryVersionLocator.Raw, ElevenLabs.PronunciationDictionaryVersionLocator>;
export declare namespace PronunciationDictionaryVersionLocator {
    interface Raw {
        pronunciation_dictionary_id: string;
        version_id?: string | null;
    }
}
