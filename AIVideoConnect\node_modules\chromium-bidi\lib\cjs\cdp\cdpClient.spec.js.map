{"version": 3, "file": "cdpClient.spec.js", "sourceRoot": "", "sources": ["../../../src/cdp/cdpClient.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2CAA6B;AAC7B,+BAA4B;AAC5B,6CAA+B;AAE/B,wEAA8C;AAE9C,0EAA6D;AAE7D,yDAAiD;AAEjD,IAAI,CAAC,GAAG,CAAC,0BAAc,CAAC,CAAC;AAEzB,MAAM,cAAc,GAAG,SAAS,CAAC;AACjC,MAAM,iBAAiB,GAAG,SAAS,CAAC;AAEpC,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;QACtE,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;YACxC,EAAE,EAAE,CAAC;YACL,MAAM,EAAE,uBAAuB;YAC/B,MAAM,EAAE;gBACN,QAAQ,EAAE,cAAc;aACzB;SACF,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,IAAI,qCAAa,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAG,IAAI,gCAAa,CAAC,aAAa,CAAC,CAAC;QAEvD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAChD,KAAK,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;YAClD,QAAQ,EAAE,cAAc;SACzB,CAAC,CAAC;QAEH,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAChC,aAAa,CAAC,WAAW,EACzB,kBAAkB,CACnB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uEAAuE,EAAE,KAAK,IAAI,EAAE;QACrF,MAAM,aAAa,GAAG,IAAI,qCAAa,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAG,IAAI,gCAAa,CAAC,aAAa,CAAC,CAAC;QAEvD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAEhD,+CAA+C;QAC/C,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;YACpE,QAAQ,EAAE,cAAc;SACzB,CAAC,CAAC;QAEH,+BAA+B;QAC/B,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAEnD,kDAAkD;QAClD,MAAM,aAAa,CAAC,sBAAsB,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC,CAAC;QAEhE,+CAA+C;QAC/C,MAAM,IAAA,aAAM,EAAC,cAAc,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wGAAwG,EAAE,KAAK,IAAI,EAAE;QACtH,MAAM,aAAa,GAAG,IAAI,qCAAa,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAG,IAAI,gCAAa,CAAC,aAAa,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAEhD,MAAM,eAAe,GAAG;YACtB,UAAU,EAAE,CAAC;SACmC,CAAC;QACnD,MAAM,eAAe,GAAG;YACtB,aAAa,EAAE,CAAC;SACgC,CAAC;QACnD,MAAM,cAAc,GAAG,EAAC,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,eAAe,EAAC,CAAC;QACxD,MAAM,cAAc,GAAG,EAAC,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,eAAe,EAAC,CAAC;QAExD,mDAAmD;QACnD,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;YACrE,QAAQ,EAAE,cAAc;SACzB,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;YACrE,QAAQ,EAAE,iBAAiB;SAC5B,CAAC,CAAC;QAEH,+BAA+B;QAC/B,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAEpD,+CAA+C;QAC/C,MAAM,aAAa,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAC3D,6CAA6C;QAC7C,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC;QAE5C,+CAA+C;QAC/C,MAAM,aAAa,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAC3D,4CAA4C;QAC5C,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC;QAE5C,IAAA,aAAM,EAAC,aAAa,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACrD,IAAA,aAAM,EAAC,aAAa,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;QACtE,MAAM,aAAa,GAAG,IAAI,qCAAa,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAG,IAAI,gCAAa,CAAC,aAAa,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAEhD,4BAA4B;QAC5B,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACrC,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAEnC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACnC,SAAS,CAAC,EAAE,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;QAEvD,oBAAoB;QACpB,MAAM,aAAa,CAAC,sBAAsB,CAAC;YACzC,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE,EAAC,QAAQ,EAAE,cAAc,EAAC;SACnC,CAAC,CAAC;QAEH,oCAAoC;QACpC,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAChC,eAAe,EACf,yBAAyB,EACzB,EAAC,QAAQ,EAAE,cAAc,EAAC,CAC3B,CAAC;QACF,eAAe,CAAC,YAAY,EAAE,CAAC;QAE/B,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,aAAa,EAAE;YAChD,QAAQ,EAAE,cAAc;SACzB,CAAC,CAAC;QACH,aAAa,CAAC,YAAY,EAAE,CAAC;QAE7B,wBAAwB;QACxB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QACpC,SAAS,CAAC,GAAG,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;QAExD,0BAA0B;QAC1B,MAAM,aAAa,CAAC,sBAAsB,CAAC;YACzC,MAAM,EAAE,EAAC,QAAQ,EAAE,cAAc,EAAC;SACnC,CAAC,CAAC;QAEH,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACxC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oFAAoF,EAAE,KAAK,IAAI,EAAE;YAClG,MAAM,aAAa,GAAG,IAAI,qCAAa,EAAE,CAAC;YAC1C,MAAM,aAAa,GAAG,IAAI,gCAAa,CAAC,aAAa,CAAC,CAAC;YACvD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;YAEhD,+CAA+C;YAC/C,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBACpE,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,+BAA+B;YAC/B,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAEnD,kDAAkD;YAClD,MAAM,aAAa,CAAC,sBAAsB,CAAC;gBACzC,EAAE,EAAE,CAAC;gBACL,MAAM,EAAE,EAAC,QAAQ,EAAE,cAAc,EAAC;aACnC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,IAAA,aAAM,EAAC,cAAc,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;gBACpD,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;YACtF,MAAM,aAAa,GAAG,IAAI,qCAAa,EAAE,CAAC;YAC1C,MAAM,aAAa,GAAG,IAAI,gCAAa,CAAC,aAAa,CAAC,CAAC;YACvD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;YAEhD,MAAM,aAAa,GAAG;gBACpB,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,oBAAoB;aAC9B,CAAC;YAEF,+CAA+C;YAC/C,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBACpE,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,+BAA+B;YAC/B,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAEnD,kDAAkD;YAClD,MAAM,aAAa,CAAC,sBAAsB,CAAC;gBACzC,EAAE,EAAE,CAAC;gBACL,KAAK,EAAE,aAAa;aACrB,CAAC,CAAC;YAEH,yCAAyC;YACzC,MAAM,IAAA,aAAM,EAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CACxD,aAAoB,CACrB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}