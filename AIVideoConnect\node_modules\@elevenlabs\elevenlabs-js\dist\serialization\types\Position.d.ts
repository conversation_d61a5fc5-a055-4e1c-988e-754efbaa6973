/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const Position: core.serialization.ObjectSchema<serializers.Position.Raw, ElevenLabs.Position>;
export declare namespace Position {
    interface Raw {
        x?: number | null;
        y?: number | null;
    }
}
