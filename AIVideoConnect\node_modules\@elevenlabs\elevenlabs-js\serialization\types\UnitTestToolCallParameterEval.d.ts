/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ExactParameterEvaluationStrategy } from "./ExactParameterEvaluationStrategy";
import { LlmParameterEvaluationStrategy } from "./LlmParameterEvaluationStrategy";
import { RegexParameterEvaluationStrategy } from "./RegexParameterEvaluationStrategy";
export declare const UnitTestToolCallParameterEval: core.serialization.Schema<serializers.UnitTestToolCallParameterEval.Raw, ElevenLabs.UnitTestToolCallParameterEval>;
export declare namespace UnitTestToolCallParameterEval {
    type Raw = UnitTestToolCallParameterEval.Exact | UnitTestToolCallParameterEval.Llm | UnitTestToolCallParameterEval.Regex;
    interface Exact extends ExactParameterEvaluationStrategy.Raw {
        type: "exact";
    }
    interface Llm extends LlmParameterEvaluationStrategy.Raw {
        type: "llm";
    }
    interface Regex extends RegexParameterEvaluationStrategy.Raw {
        type: "regex";
    }
}
