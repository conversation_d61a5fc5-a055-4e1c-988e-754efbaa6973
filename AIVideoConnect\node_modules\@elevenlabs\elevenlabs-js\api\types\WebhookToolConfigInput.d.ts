/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * A webhook tool is a tool that calls an external webhook from our server
 */
export interface WebhookToolConfigInput {
    name: string;
    description: string;
    /** The maximum time in seconds to wait for the tool call to complete. Must be between 5 and 120 seconds (inclusive). */
    responseTimeoutSecs?: number;
    /** If true, the user will not be able to interrupt the agent while this tool is running. */
    disableInterruptions?: boolean;
    /** If true, the agent will speak before the tool call. */
    forcePreToolSpeech?: boolean;
    /** Configuration for extracting values from tool responses and assigning them to dynamic variables */
    assignments?: ElevenLabs.DynamicVariableAssignment[];
    /** The schema for the outgoing webhoook, including parameters and URL specification */
    apiSchema: ElevenLabs.WebhookToolApiSchemaConfigInput;
    /** Configuration for dynamic variables */
    dynamicVariables?: ElevenLabs.DynamicVariablesConfig;
}
