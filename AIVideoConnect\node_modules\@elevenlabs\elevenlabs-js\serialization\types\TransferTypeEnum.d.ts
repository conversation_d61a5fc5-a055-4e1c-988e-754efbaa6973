/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TransferTypeEnum: core.serialization.Schema<serializers.TransferTypeEnum.Raw, ElevenLabs.TransferTypeEnum>;
export declare namespace TransferTypeEnum {
    type Raw = "conference" | "sip_refer";
}
